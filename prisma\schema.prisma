generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
}

model User {
  id                     String       @id @default(uuid())
  phoneNumber            String       @unique
  firstName              String
  lastName               String
  password               String
  isPhoneVerified        Boolean      @default(false)
  verificationCode       String?
  verificationCodeSentAt DateTime?
  driverStatus           DriverStatus @default(NONE)

  IdCardFrontUrl   String?
  IdCardBackUrl    String?
  PersonalPhotoUrl String?

  car              Car?
  orders           Order[]
  trips            Trip[]             @relation("DriverTrips")
  markedPlaces     MarkedPlace[]
  refreshTokens    RefreshToken[]
  suggestedOrders  Order[]            @relation("SuggestedOrders")
  transactionsFrom MoneyTransaction[] @relation("TransactionsFrom")
  transactionsTo   MoneyTransaction[] @relation("TransactionsTo")

  @@index([phoneNumber])
  @@index([driverStatus])
}

model Car {
  id     String @id @default(uuid())
  userId String @unique

  make         String
  model        String
  year         Int
  licensePlate String?

  photos CarPhoto[] @relation("CarPhotos")

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([make, model])
}

model CarPhoto {
  id    String @id @default(uuid())
  carId String

  photoUrl String

  car Car @relation("CarPhotos", fields: [carId], references: [id])

  @@index([carId])
}

// Position models
model StopPoint {
  id        String @id @default(uuid())
  name      String
  latitude  Float
  longitude Float
}

model MarkedPlace {
  id        String @id @default(uuid())
  userId    String
  name      String
  latitude  Float
  longitude Float

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Point {
  id        String @id @default(uuid())
  latitude  Float
  longitude Float

  pickupOrders  Order[] @relation("PickupPoint")
  dropoffOrders Order[] @relation("DropoffPoint")
}

enum DriverStatus {
  NONE
  IN_REVIEW // Driver data is in review for admins
  PENDING_ONSITE_REVIEW
  REJECTED
  APPROVED
}

enum OrderStatus {
  PENDING
  SUGGESTED_FOR_DRIVER // In order to prevent suggesting an order for multiple drivers at a time
  CONFIRMED
  CANCLED
  COMPLETED
}

enum TripStatus {
  DRIVER_DIDNT_ARRIVE
  DRIVER_WAITING_CLIENT
  DRIVER_WITH_CLIENT
  CANCLED
  FINISHED
}

model Order {
  id        String      @id @default(uuid())
  userId    String
  status    OrderStatus @default(PENDING)
  createdAt DateTime    @default(now())

  // Point relations
  pickupPointId  String?
  dropoffPointId String?

  // Suggestion tracking
  lastSuggestedAt       DateTime? // When the order was last suggested to a driver
  lastSuggestedDriverId String? // ID of the last driver this order was suggested to

  // Trip relation
  tripId String? @unique // Make unique to ensure one-to-one relation

  // Pricing fields
  initialPrice Float? // Initial calculated price
  finalPrice   Float? // Final price after trip completion

  // Relations
  user                User               @relation(fields: [userId], references: [id])
  pickupPoint         Point?             @relation("PickupPoint", fields: [pickupPointId], references: [id])
  dropoffPoint        Point?             @relation("DropoffPoint", fields: [dropoffPointId], references: [id])
  lastSuggestedDriver User?              @relation("SuggestedOrders", fields: [lastSuggestedDriverId], references: [id])
  trip                Trip?              @relation(fields: [tripId], references: [id])
  transactions        MoneyTransaction[]

  @@index([userId])
  @@index([pickupPointId])
  @@index([dropoffPointId])
  @@index([lastSuggestedDriverId])
  @@index([tripId])
}

model Trip {
  id                       String     @id @default(uuid())
  driverId                 String
  status                   TripStatus @default(DRIVER_DIDNT_ARRIVE)
  currentLocationLatitude  Float?
  currentLocationLongitude Float?
  createdAt                DateTime   @default(now())

  driver User   @relation("DriverTrips", fields: [driverId], references: [id])
  order  Order?

  @@index([driverId])
}

enum TransactionType {
  CLIENT_TO_DRIVER
  DRIVER_TO_COMPANY
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

model MoneyTransaction {
  id     String            @id @default(uuid())
  type   TransactionType
  status TransactionStatus @default(PENDING)
  amount Float // Amount in USD

  // Order reference
  orderId String

  // Participants
  fromUserId String? // Null for company transactions
  toUserId   String? // Null for company transactions

  // Metadata
  description String?
  createdAt   DateTime  @default(now())
  completedAt DateTime?

  // Relations
  order    Order @relation(fields: [orderId], references: [id])
  fromUser User? @relation("TransactionsFrom", fields: [fromUserId], references: [id])
  toUser   User? @relation("TransactionsTo", fields: [toUserId], references: [id])

  @@index([orderId])
  @@index([fromUserId])
  @@index([toUserId])
  @@index([type])
  @@index([status])
  @@map("Transaction")
}
