<div
    class="pointer-events-auto m-0 flex h-full max-h-[99dvh] flex-col overflow-auto bg-background-color-100 p-0"
>
    <!-- Pending Orders Dialog -->
    <p-dialog
        header="You have a trip in progress"
        [modal]="true"
        [visible]="showPendingOrderDialog()"
        [closable]="false"
        [style]="{ width: '90vw', maxWidth: '400px' }"
        styleClass="!bg-background-color-100"
    >
        <div class="py-4 text-center">
            <i class="pi pi-car mb-4 text-4xl text-text-color-100"></i>
            <p class="mb-6 text-base text-text-color-100">
                You have an active trip. Would you like to continue tracking it
                or start a new trip?
            </p>

            @if (pendingOrders().length > 0) {
                <div class="mb-6 rounded-xl bg-main-color-600 p-4">
                    <div class="flex items-center justify-between">
                        <span class="font-medium text-text-color-100"
                            >Trip #{{ pendingOrders()[0].id.slice(-8) }}</span
                        >
                        <p-tag
                            [value]="pendingOrders()[0].status"
                            severity="info"
                            styleClass="!bg-main-color-100 !text-background-color-200 !rounded !text-xs !px-2 !py-1"
                        >
                        </p-tag>
                    </div>
                </div>
            }
        </div>

        <ng-template pTemplate="footer">
            <div class="flex w-full flex-col gap-3">
                <button
                    class="w-full rounded-md bg-background-color-200 p-2 text-background-color-100 transition-colors duration-200 hover:bg-main-color-700"
                    (click)="handlePendingOrder(true)"
                >
                    Continue Trip
                </button>
                <button
                    class="w-full rounded-md border border-background-color-300 p-2 text-text-color-100 transition-colors duration-200 hover:bg-background-color-200"
                    (click)="handlePendingOrder(false)"
                >
                    Start New Trip
                </button>
            </div>
        </ng-template>
    </p-dialog>

    <!-- Step Content -->
    <div
        class="flex h-full min-h-0 flex-1 flex-col overflow-auto bg-background-color-100"
    >
        <!-- Step 1: Checking Pending Orders -->
        @if (currentStep() === OrderStep.CHECK_PENDING) {
            <div
                class="flex flex-1 flex-col items-center justify-center bg-background-color-100 p-8 text-center"
            >
                @if (isLoading()) {
                    <div
                        class="mb-6 h-12 w-12 animate-spin rounded-full border-4 border-main-color-600 border-t-transparent"
                    ></div>
                    <h2 class="mb-2 text-2xl font-semibold text-text-color-100">
                        Setting up your ride...
                    </h2>
                } @else {
                    <i
                        class="pi pi-check-circle mb-4 text-5xl text-main-color-600"
                    ></i>
                    <h2 class="mb-2 text-2xl font-semibold text-text-color-100">
                        Ready to go!
                    </h2>
                }
            </div>
        }

        <!-- Step 2: Select Pickup Location -->
        @if (currentStep() === OrderStep.SELECT_PICKUP) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Pick your pickup location
                        </h3>
                    </div>
                </div>

                <!-- Marked Places Horizontal Scroll -->
                @if (markedPlaces().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                                    (click)="selectMarkedPlace(place)"
                                >
                                    <span>{{ place.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Pickup"
                        placeholder="Enter pickup location"
                        (locationSelected)="onPickupSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 3: Select Dropoff Location -->
        @if (currentStep() === OrderStep.SELECT_DROPOFF) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 bg-background-color-100 p-4"
                >
                    <div
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                    >
                        <i class="pi pi-check text-sm"></i>
                    </div>
                    <div class="min-w-0 flex-1">
                        <h4
                            class="mb-1 text-sm font-medium text-text-color-100"
                        >
                            Pickup
                        </h4>
                        <p class="truncate text-sm text-text-color-300">
                            {{
                                pickupAddress() ||
                                    pickupLocation()!.lat.toFixed(4) +
                                        ", " +
                                        pickupLocation()!.lng.toFixed(4)
                            }}
                        </p>
                    </div>
                </div>

                <!-- Dropoff Selection -->
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-red-600 text-background-color-100"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Where to?
                        </h3>
                    </div>
                </div>

                <!-- Marked Places Horizontal Scroll for Dropoff -->
                @if (markedPlaces().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                                    (click)="selectMarkedPlaceAsDropoff(place)"
                                >
                                    <span>{{ place.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="flex min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Destination"
                        placeholder="Enter destination"
                        (locationSelected)="onDropoffSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 4: Calculating Price -->
        @if (currentStep() === OrderStep.CALCULATE_PRICE) {
            <div
                class="flex flex-1 flex-col items-center justify-center bg-background-color-100 p-8 text-center"
            >
                <div
                    class="mb-6 h-12 w-12 animate-spin rounded-full border-4 border-main-color-600 border-t-transparent"
                ></div>
                <h2 class="mb-2 text-2xl font-semibold text-text-color-100">
                    Finding your price...
                </h2>
                <p class="max-w-md text-text-color-300">
                    We're calculating the best price based on distance and
                    demand
                </p>
            </div>
        }

        <!-- Step 5: Confirm Order -->
        @if (currentStep() === OrderStep.CONFIRM_ORDER) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <!-- Price Card - Moved to Top -->
                <div class="border-b border-background-color-300 p-6">
                    <div
                        class="flex items-center justify-between rounded-xl border border-background-color-300 bg-background-color-100 p-4 shadow-shadow-200"
                    >
                        <div class="flex items-center gap-4">
                            <div
                                class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-background-color-100"
                            >
                                <i class="pi pi-car text-lg"></i>
                            </div>
                            <div>
                                @if (estimatedDurationMinutes()) {
                                    <p
                                        class="text-sm font-medium text-text-color-100"
                                    >
                                        {{ estimatedDurationMinutes() }} min
                                    </p>
                                }
                                @if (estimatedDistanceKm()) {
                                    <p class="text-sm text-text-color-300">
                                        {{ estimatedDistanceKm() }} km
                                    </p>
                                }
                            </div>
                        </div>
                        <div>
                            <span class="text-2xl font-bold text-text-color-100"
                                >${{ calculatedPrice()?.toFixed(2) }}</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Route Container - Full Space Between Price Card and Buttons -->
                <div class="flex flex-1 flex-col justify-center p-6">
                    <!-- From Section - At Top -->
                    <div class="mb-6">
                        <div class="flex items-start gap-4">
                            <div
                                class="mt-1 flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                            >
                                <i class="pi pi-map-marker text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h4
                                    class="mb-1 text-sm font-medium text-text-color-100"
                                >
                                    From
                                </h4>
                                <div>
                                    <p
                                        class="text-base font-medium text-text-color-100"
                                    >
                                        {{ getDetailedPickupAddress().primary }}
                                    </p>
                                    @if (getDetailedPickupAddress().secondary) {
                                        <p class="text-sm text-text-color-300">
                                            {{
                                                getDetailedPickupAddress()
                                                    .secondary
                                            }}
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Connecting Line -->
                    <div class="mb-6 flex justify-start">
                        <div class="flex w-10 justify-center">
                            <div
                                class="h-8 w-0.5 bg-background-color-300"
                            ></div>
                        </div>
                    </div>

                    <!-- To Section - At Bottom -->
                    <div>
                        <div class="flex items-start gap-4">
                            <div
                                class="mt-1 flex h-10 w-10 items-center justify-center rounded-full bg-red-600 text-background-color-100"
                            >
                                <i class="pi pi-map-marker text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h4
                                    class="mb-1 text-sm font-medium text-text-color-100"
                                >
                                    To
                                </h4>
                                <div>
                                    <p
                                        class="text-base font-medium text-text-color-100"
                                    >
                                        {{
                                            getDetailedDropoffAddress().primary
                                        }}
                                    </p>
                                    @if (
                                        getDetailedDropoffAddress().secondary
                                    ) {
                                        <p class="text-sm text-text-color-300">
                                            {{
                                                getDetailedDropoffAddress()
                                                    .secondary
                                            }}
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirm Buttons -->
                <div class="space-y-3 border-t border-background-color-300 p-6">
                    <button
                        class="flex w-full items-center justify-center gap-2 rounded-lg bg-background-color-200 px-6 py-4 text-lg font-semibold text-background-color-100 shadow-shadow-200 transition-all duration-200 hover:bg-main-color-700 hover:shadow-shadow-400 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-main-color-600"
                        [disabled]="isLoading()"
                        (click)="confirmOrder()"
                    >
                        @if (isLoading()) {
                            <div
                                class="h-5 w-5 animate-spin rounded-full border-2 border-background-color-100 border-t-transparent"
                            ></div>
                            Requesting...
                        } @else {
                            <i class="pi pi-check text-sm"></i>
                            Confirm Order
                        }
                    </button>
                    <button
                        class="flex w-full items-center justify-center gap-2 rounded-lg border border-background-color-300 bg-background-color-100 px-6 py-4 text-lg font-medium text-text-color-100 transition-all duration-200 hover:bg-background-color-200 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2"
                        (click)="declineOrder()"
                    >
                        <i class="pi pi-times text-sm"></i>
                        Cancel
                    </button>
                </div>
            </div>
        }

        <!-- Step 6: Looking for Driver -->
        @if (currentStep() === OrderStep.ORDER_CREATED) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex flex-1 flex-col items-center justify-center p-8 text-center"
                >
                    <!-- Animated car icon -->
                    <div class="relative mb-8">
                        <div
                            class="flex h-20 w-20 animate-bounce items-center justify-center rounded-full bg-main-color-100 text-background-color-100"
                        >
                            <i class="pi pi-car text-3xl"></i>
                        </div>
                        <div
                            class="absolute inset-0 flex items-center justify-center"
                        >
                            <div
                                class="absolute h-20 w-20 animate-ping rounded-full border-4 border-main-color-600 opacity-30"
                            ></div>
                            <div
                                class="animation-delay-200 absolute h-32 w-32 animate-ping rounded-full border-4 border-main-color-600 opacity-20"
                            ></div>
                            <div
                                class="animation-delay-400 absolute h-44 w-44 animate-ping rounded-full border-4 border-main-color-600 opacity-10"
                            ></div>
                        </div>
                    </div>

                    <!-- Main message -->
                    <div class="mb-8">
                        <h2 class="mb-2 text-3xl font-bold text-text-color-100">
                            Finding your driver
                        </h2>
                        <p class="text-lg text-text-color-300">
                            We're connecting you with a nearby driver
                        </p>
                    </div>

                    <!-- Trip details card -->
                    <div
                        class="mb-8 w-full max-w-md rounded-xl border border-background-color-300 bg-background-color-100 p-6 shadow-shadow-200"
                    >
                        <div class="mb-6 space-y-4">
                            <div class="flex items-center gap-3">
                                <div
                                    class="h-3 w-3 rounded-full bg-green-600"
                                ></div>
                                <div class="min-w-0 flex-1">
                                    <span
                                        class="block text-sm font-medium text-text-color-100"
                                        >From</span
                                    >
                                    <span
                                        class="block truncate text-sm text-text-color-300"
                                        >{{
                                            getDetailedPickupAddress().primary
                                        }}</span
                                    >
                                </div>
                            </div>
                            <div class="flex justify-start">
                                <div class="flex w-3 justify-center">
                                    <div
                                        class="h-6 w-0.5 bg-background-color-300"
                                    ></div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div
                                    class="h-3 w-3 rounded-full bg-red-600"
                                ></div>
                                <div class="min-w-0 flex-1">
                                    <span
                                        class="block text-sm font-medium text-text-color-100"
                                        >To</span
                                    >
                                    <span
                                        class="block truncate text-sm text-text-color-300"
                                        >{{
                                            getDetailedDropoffAddress().primary
                                        }}</span
                                    >
                                </div>
                            </div>
                        </div>

                        <div
                            class="flex items-center justify-between border-t border-background-color-300 pt-4"
                        >
                            <span
                                class="text-sm font-medium text-text-color-100"
                                >Trip cost</span
                            >
                            <span class="text-lg font-bold text-text-color-100"
                                >${{ calculatedPrice()?.toFixed(2) }}</span
                            >
                        </div>
                        <div
                            class="flex items-center justify-between border-t border-background-color-300 pt-4"
                        >
                            <span
                                class="text-sm font-medium text-text-color-100"
                                >distance</span
                            >
                            <span class="text-lg font-bold text-text-color-100"
                                >${{ calculatedPrice()?.toFixed(2) }}</span
                            >
                        </div>
                    </div>

                    <!-- Cancel button -->
                    <div class="w-full max-w-md">
                        <button
                            class="flex w-full items-center justify-center gap-2 rounded-lg border border-background-color-300 bg-background-color-100 px-6 py-3 text-lg font-medium text-text-color-100 transition-all duration-200 hover:bg-background-color-200 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2"
                            (click)="cancelOrderSearch()"
                        >
                            <i class="pi pi-times text-sm"></i>
                            Cancel Trip
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
