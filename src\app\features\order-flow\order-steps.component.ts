import { CommonModule, DOCUMENT } from '@angular/common';
import {
    Component,
    computed,
    inject,
    On<PERSON><PERSON>roy,
    OnInit,
    Renderer2,
    signal,
} from '@angular/core';
import { LatLng, latLng } from 'leaflet';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { StepperModule } from 'primeng/stepper';
import { TagModule } from 'primeng/tag';
import { firstValueFrom, interval, Subscription } from 'rxjs';
import {
    MarkedPlace,
    MarkedPlaceService,
} from '../../services/marked-place.service';
import { NominatimService } from '../../services/nominatim.service';
import {
    CreateOrderDto,
    Order,
    OrderService,
    OrderStatus,
} from '../../services/order.service';
import { ValhallaService } from '../../services/valhala.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { ReverseGeocodingResult } from '../../shared/types/nominatim.types';
import { DirectionsRequest } from '../../shared/types/valhalla.types';
import { LocationPickerComponent } from './location-picker.component';
// import { PriceConfirmationComponent } from './price-confirmation.component';
import { Router } from '@angular/router';
import {
    calculateFallbackEstimates,
    isSameLocation,
} from '../../shared/helpers/helpers';

export enum OrderStep {
    CHECK_PENDING = 0,
    SELECT_PICKUP = 1,
    SELECT_DROPOFF = 2,
    CALCULATE_PRICE = 3,
    CONFIRM_ORDER = 4,
    ORDER_CREATED = 5,
}

@Component({
    selector: 'app-order-steps',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        StepperModule,
        DialogModule,
        TagModule,
        LocationPickerComponent,
    ],
    templateUrl: './order-steps.component.html',
    styles: `
        :host {
            display: block;
            flex-grow: 1;
            max-height: 97dvh;
        }
    `,
    providers: [MessageService],
})
export class OrderStepsComponent implements OnInit, OnDestroy {
    services = injectMany({
        OrderService,
        MarkedPlaceService,
        MessageService,
        Router,
        NominatimService,
        ValhallaService,
    });

    OrderStep = OrderStep;

    // State management
    currentStep = signal<OrderStep>(OrderStep.CHECK_PENDING);
    pendingOrders = signal<Order[]>([]);
    pickupLocation = signal<LatLng | null>(null);
    dropoffLocation = signal<LatLng | null>(null);
    pickupAddress = signal<string>('');
    dropoffAddress = signal<string>('');
    pickupAddressDetails = signal<ReverseGeocodingResult | null>(null);
    dropoffAddressDetails = signal<ReverseGeocodingResult | null>(null);
    calculatedPrice = signal<number | null>(null);
    estimatedDurationMinutes = signal<number | null>(null);
    estimatedDistanceKm = signal<number | null>(null);
    isLoading = signal<boolean>(false);
    showPendingOrderDialog = signal<boolean>(false);
    markedPlaces = signal<MarkedPlace[]>([]);
    currentOrderId = signal<string | null>(null);

    // Polling for order status updates
    private statusPollingSubscription: Subscription | null = null;

    // Computed properties
    canProceedToDropoff = computed(() => this.pickupLocation() !== null);
    canCalculatePrice = computed(
        () => this.pickupLocation() !== null && this.dropoffLocation() !== null,
    );
    orderDto = computed<CreateOrderDto | null>(() => {
        const pickup = this.pickupLocation();
        const dropoff = this.dropoffLocation();

        if (!pickup || !dropoff) return null;

        return {
            pickupLatitude: pickup.lat,
            pickupLongitude: pickup.lng,
            dropoffLatitude: dropoff.lat,
            dropoffLongitude: dropoff.lng,
        };
    });

    ngOnInit(): void {
        this.checkPendingOrders();
        this.loadMarkedPlaces();
    }

    ngOnDestroy(): void {
        // Stop polling for order status updates
        this.stopOrderStatusPolling();
    }

    /**
     * Step 1: Check for pending orders
     */
    checkPendingOrders(): void {
        this.isLoading.set(true);
        this.services.OrderService.getUserOrdersNotComplete().subscribe({
            next: (response) => {
                this.isLoading.set(false);
                if (response.data) {
                    // Filter for pending orders (PENDING or SUGGESTED_FOR_DRIVER status)
                    const pendingOrders = response.data.filter(
                        (order) =>
                            order.status === 'PENDING' ||
                            order.status === 'SUGGESTED_FOR_DRIVER',
                    );

                    if (pendingOrders.length > 0) {
                        this.pendingOrders.set(pendingOrders);
                        this.showPendingOrderDialog.set(true);
                    } else {
                        // No pending orders, proceed to location selection
                        this.proceedToLocationSelection();
                    }
                } else {
                    // No orders at all, proceed to location selection
                    this.proceedToLocationSelection();
                }
            },
            error: () => {
                this.isLoading.set(false);
                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to check pending orders',
                });
                // Proceed anyway
                this.proceedToLocationSelection();
            },
        });
    }
    handlePendingOrder(continueWithExisting: boolean): void {
        this.showPendingOrderDialog.set(false);

        if (continueWithExisting && this.pendingOrders().length > 0) {
            this.currentStep.set(OrderStep.ORDER_CREATED);
        } else {
            // Start new order process
            this.proceedToLocationSelection();
        }
    }

    proceedToLocationSelection(): void {
        this.currentStep.set(OrderStep.SELECT_PICKUP);
    }

    /**
     * Load user's marked places
     */
    loadMarkedPlaces(): void {
        this.services.MarkedPlaceService.getAllMarkedPlaces().subscribe(
            (response) => {
                if (response.data) {
                    this.markedPlaces.set(response.data);
                }
            },
        );
    }

    /**
     * Select a marked place as pickup location
     */
    selectMarkedPlace(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);
        this.pickupLocation.set(location);
        this.pickupAddress.set(markedPlace.name);
        this.currentStep.set(OrderStep.SELECT_DROPOFF);
    }

    /**
     * Select a marked place as dropoff location
     */
    selectMarkedPlaceAsDropoff(markedPlace: MarkedPlace): void {
        const location = latLng(markedPlace.latitude, markedPlace.longitude);

        // Check if dropoff is too close to pickup (same location)
        if (isSameLocation(this.pickupLocation(), location)) {
            this.services.MessageService.add({
                severity: 'warn',
                summary: 'Invalid Destination',
                detail: 'Pickup and dropoff locations cannot be the same. Please choose a different destination.',
            });
            return;
        }

        this.dropoffLocation.set(location);
        this.dropoffAddress.set(markedPlace.name);
        this.calculatePrice();
    }

    /**
     * Get route information from Valhalla
     */
    private async getRouteInformation(dto: CreateOrderDto): Promise<void> {
        try {
            console.log('🚗 Getting route information from Valhalla...', dto);

            const directionsRequest: DirectionsRequest = {
                locations: [
                    {
                        lat: dto.pickupLatitude,
                        lon: dto.pickupLongitude,
                    },
                    {
                        lat: dto.dropoffLatitude,
                        lon: dto.dropoffLongitude,
                    },
                ],
                costing: 'auto',
                directions_options: {
                    units: 'kilometers',
                    language: 'en',
                },
            };

            console.log('📍 Directions request:', directionsRequest);

            const response = await firstValueFrom(
                this.services.ValhallaService.getDirections(directionsRequest),
            );

            console.log('🗺️ Valhalla response:', response);

            if (response?.trip?.summary) {
                // Set estimated duration (convert from seconds to minutes)
                const durationMinutes = Math.round(
                    response.trip.summary.time / 60,
                );
                const distanceKm =
                    Math.round(response.trip.summary.length * 100) / 100;

                console.log(
                    `⏱️ Calculated duration: ${durationMinutes} minutes`,
                );
                console.log(`📏 Calculated distance: ${distanceKm} km`);

                this.estimatedDurationMinutes.set(durationMinutes);
                this.estimatedDistanceKm.set(distanceKm);

                console.log('✅ Duration and distance set successfully');
            } else {
                console.warn('⚠️ No trip summary in Valhalla response');
            }
        } catch (error) {
            console.error('❌ Failed to get route information:', error);
            // Use fallback calculation based on straight-line distance
            const r = calculateFallbackEstimates(dto);
            this.estimatedDistanceKm.set(r.distance);
            this.estimatedDurationMinutes.set(r.time);
            throw error; // Re-throw to let caller handle
        }
    }

    /**
     * Step 2: Handle pickup location selection
     */
    onPickupSelected(location: LatLng): void {
        this.pickupLocation.set(location);
        this.resolveAddress(location, 'pickup');
        this.currentStep.set(OrderStep.SELECT_DROPOFF);
    }

    /**
     * Step 3: Handle dropoff location selection
     */
    onDropoffSelected(location: LatLng): void {
        // Check if dropoff is too close to pickup (same location)
        if (isSameLocation(this.pickupLocation(), location)) {
            this.services.MessageService.add({
                severity: 'warn',
                summary: 'Invalid Destination',
                detail: 'Pickup and dropoff locations cannot be the same. Please choose a different destination.',
            });
            return;
        }

        this.dropoffLocation.set(location);
        this.resolveAddress(location, 'dropoff');
        this.calculatePrice();
    }

    /**
     * Step 4: Calculate initial price and trip duration
     */
    calculatePrice(): void {
        const dto = this.orderDto();
        if (!dto) return;

        this.isLoading.set(true);
        this.currentStep.set(OrderStep.CALCULATE_PRICE);

        // First get the route information from Valhalla
        this.getRouteInformation(dto)
            .then(() => {
                // Then get the price from backend
                this.services.OrderService.calculateOrderPrice(dto).subscribe({
                    next: (response) => {
                        this.isLoading.set(false);
                        if (response.data !== null) {
                            this.calculatedPrice.set(response.data);
                            this.currentStep.set(OrderStep.CONFIRM_ORDER);
                        } else {
                            this.services.MessageService.add({
                                severity: 'error',
                                summary: 'Error',
                                detail: 'Failed to calculate price',
                            });
                            this.resetToLocationSelection();
                        }
                    },
                    error: () => {
                        this.isLoading.set(false);
                        this.services.MessageService.add({
                            severity: 'error',
                            summary: 'Error',
                            detail: 'Failed to calculate price',
                        });
                        this.resetToLocationSelection();
                    },
                });
            })
            .catch((error) => {
                // If route calculation fails, still proceed with price calculation
                console.log(
                    '🔄 Route calculation failed, using fallback and continuing with price calculation',
                );
                this.services.OrderService.calculateOrderPrice(dto).subscribe({
                    next: (response) => {
                        this.isLoading.set(false);
                        if (response.data !== null) {
                            this.calculatedPrice.set(response.data);
                            this.currentStep.set(OrderStep.CONFIRM_ORDER);
                        } else {
                            this.services.MessageService.add({
                                severity: 'error',
                                summary: 'Error',
                                detail: 'Failed to calculate price',
                            });
                            this.resetToLocationSelection();
                        }
                    },
                    error: () => {
                        this.isLoading.set(false);
                        this.services.MessageService.add({
                            severity: 'error',
                            summary: 'Error',
                            detail: 'Failed to calculate price',
                        });
                        this.resetToLocationSelection();
                    },
                });
            });
    }

    /**
     * Step 5: Confirm order creation
     */
    confirmOrder(): void {
        const dto = this.orderDto();
        if (!dto) return;

        this.isLoading.set(true);

        this.services.OrderService.createOrder(dto).subscribe({
            next: (response) => {
                this.isLoading.set(false);
                if (response.data) {
                    this.currentOrderId.set(response.data.id);
                    this.currentStep.set(OrderStep.ORDER_CREATED);

                    // Start polling for order status updates
                    this.startOrderStatusPolling();
                } else {
                    // Handle case where response is successful but data is null
                    // The HttpService wraps the HttpErrorResponse in the error property

                    // DEBUG: Log the complete error structure
                    console.log(
                        '=== ORDER CREATION ERROR DEBUG (in next handler) ===',
                    );
                    console.log('Full response:', response);
                    console.log('response.error:', response?.error);
                    console.log(
                        'response.error.error:',
                        response?.error?.error,
                    );
                    console.log(
                        'response.error.error.message:',
                        response?.error?.error?.message,
                    );
                    console.log(
                        'response.error.message:',
                        response?.error?.message,
                    );
                    console.log('===================================');

                    let errorMessage = 'Failed to create order';

                    // The backend response is in response.error.error (HttpErrorResponse.error)
                    if (response?.error?.error?.message) {
                        errorMessage = response.error.error.message;
                    } else if (response?.error?.error?.detail) {
                        errorMessage = response.error.error.detail;
                    } else if (response?.error?.message) {
                        errorMessage = response.error.message;
                    } else if (response?.error?.detail) {
                        errorMessage = response.error.detail;
                    }

                    console.log('Final error message:', errorMessage);

                    this.services.MessageService.add({
                        severity: 'error',
                        summary: 'Order Creation Failed',
                        detail: errorMessage,
                    });
                }
            },
            error: (response) => {
                this.isLoading.set(false);

                // DEBUG: Log the complete error structure
                console.log('=== ORDER CREATION ERROR DEBUG ===');
                console.log('Full response:', response);
                console.log('response.error:', response?.error);
                console.log(
                    'response.error.message:',
                    response?.error?.message,
                );
                console.log('response.message:', response?.message);
                console.log('typeof response.error:', typeof response?.error);
                console.log('===================================');

                // Extract the specific error message from the backend
                let errorMessage = 'Failed to create order';

                // The backend response is wrapped in response.error by Angular HTTP
                if (response?.error?.message) {
                    errorMessage = response.error.message;
                } else if (response?.error?.detail) {
                    errorMessage = response.error.detail;
                } else if (response?.message) {
                    errorMessage = response.message;
                } else if (typeof response?.error === 'string') {
                    errorMessage = response.error;
                }

                console.log('Final error message:', errorMessage);

                this.services.MessageService.add({
                    severity: 'error',
                    summary: 'Order Creation Failed',
                    detail: errorMessage,
                });
            },
        });
    }

    /**
     * Cancel order search - return to main screen
     */
    cancelOrderSearch(): void {
        this.stopOrderStatusPolling();
        this.services.Router.navigate(['/main']);
    }

    /**
     * Start polling for order status updates every 3 seconds
     */
    private startOrderStatusPolling(): void {
        // Clear any existing polling
        this.stopOrderStatusPolling();

        // Start new polling every 3 seconds
        this.statusPollingSubscription = interval(3000).subscribe(() => {
            this.checkOrderStatus();
        });
    }

    /**
     * Stop polling for order status updates
     */
    private stopOrderStatusPolling(): void {
        if (this.statusPollingSubscription) {
            this.statusPollingSubscription.unsubscribe();
            this.statusPollingSubscription = null;
        }
    }

    /**
     * Check the current order status
     */
    private checkOrderStatus(): void {
        const orderId = this.currentOrderId();
        if (!orderId) return;

        this.services.OrderService.getUserOrders().subscribe({
            next: (response) => {
                if (response.data) {
                    const currentOrder = response.data.find(
                        (order) => order.id === orderId,
                    );
                    if (currentOrder) {
                        // Check if status changed to CONFIRMED
                        if (
                            currentOrder.status === OrderStatus.CONFIRMED &&
                            currentOrder.tripId
                        ) {
                            this.stopOrderStatusPolling();
                            // Navigate to trip info page
                            this.services.Router.navigate([
                                '/main/trip',
                                currentOrder.tripId,
                            ]);
                        }
                    }
                }
            },
            error: (error) => {
                console.error('Error checking order status:', error);
                // Continue polling even if there's an error
            },
        });
    }

    /**
     * Decline order - return to location selection
     */
    declineOrder(): void {
        this.resetToLocationSelection();
    }

    /**
     * Reset to location selection
     */
    resetToLocationSelection(): void {
        this.calculatedPrice.set(null);
        this.estimatedDurationMinutes.set(null);
        this.estimatedDistanceKm.set(null);
        this.currentStep.set(OrderStep.SELECT_PICKUP);
    }

    /**
     * Resolve address from coordinates using reverse geocoding
     */
    resolveAddress(location: LatLng, type: 'pickup' | 'dropoff'): void {
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                const address = this.formatAddress(result);
                if (type === 'pickup') {
                    this.pickupAddress.set(address);
                    this.pickupAddressDetails.set(result);
                } else {
                    this.dropoffAddress.set(address);
                    this.dropoffAddressDetails.set(result);
                }
            },
            error: () => {
                // Fallback to coordinates if reverse geocoding fails
                const coordsText = `${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}`;
                if (type === 'pickup') {
                    this.pickupAddress.set(coordsText);
                    this.pickupAddressDetails.set(null);
                } else {
                    this.dropoffAddress.set(coordsText);
                    this.dropoffAddressDetails.set(null);
                }
            },
        });
    }

    /**
     * Format address from reverse geocoding result
     */
    private formatAddress(result: any): string {
        if (!result) return 'Unknown location';

        const address = result.display_name || '';
        if (address.length > 60) {
            // Extract main parts: road, neighbourhood, city
            const parts = [];
            if (result.address?.road) parts.push(result.address.road);
            if (result.address?.neighbourhood)
                parts.push(result.address.neighbourhood);
            if (
                result.address?.city ||
                result.address?.town ||
                result.address?.village
            ) {
                parts.push(
                    result.address.city ||
                        result.address.town ||
                        result.address.village,
                );
            }
            return (
                parts.slice(0, 2).join(', ') || address.substring(0, 60) + '...'
            );
        }
        return address;
    }

    /**
     * Format detailed address for confirmation screen
     */
    getDetailedPickupAddress(): { primary: string; secondary: string } {
        const details = this.pickupAddressDetails();
        if (!details || !details.address) {
            return {
                primary: this.pickupAddress() || 'Unknown location',
                secondary: '',
            };
        }

        const addr = details.address;
        const primary = addr.road || addr['name'] || 'Location';
        const secondaryParts = [];

        if (addr['neighbourhood']) secondaryParts.push(addr['neighbourhood']);
        if (addr.city || addr['town'] || addr['village']) {
            secondaryParts.push(addr.city || addr['town'] || addr['village']);
        }
        if (addr.state) secondaryParts.push(addr.state);

        return {
            primary: primary,
            secondary: secondaryParts.join(', '),
        };
    }

    /**
     * Format detailed address for confirmation screen
     */
    getDetailedDropoffAddress(): { primary: string; secondary: string } {
        const details = this.dropoffAddressDetails();
        if (!details || !details.address) {
            return {
                primary: this.dropoffAddress() || 'Unknown location',
                secondary: '',
            };
        }

        const addr = details.address;
        const primary = addr.road || addr['name'] || 'Location';
        const secondaryParts = [];

        if (addr['neighbourhood']) secondaryParts.push(addr['neighbourhood']);
        if (addr.city || addr['town'] || addr['village']) {
            secondaryParts.push(addr.city || addr['town'] || addr['village']);
        }
        if (addr.state) secondaryParts.push(addr.state);

        return {
            primary: primary,
            secondary: secondaryParts.join(', '),
        };
    }

    /**
     * Start over - reset everything
     */
    startOver(): void {
        this.stopOrderStatusPolling();
        this.currentOrderId.set(null);
        this.pickupLocation.set(null);
        this.dropoffLocation.set(null);
        this.pickupAddress.set('');
        this.dropoffAddress.set('');
        this.pickupAddressDetails.set(null);
        this.dropoffAddressDetails.set(null);
        this.calculatedPrice.set(null);
        this.estimatedDurationMinutes.set(null);
        this.estimatedDistanceKm.set(null);
        this.currentStep.set(OrderStep.CHECK_PENDING);
        this.checkPendingOrders();
    }

    /**
     * Go back to previous step
     */
    goBack(): void {
        switch (this.currentStep()) {
            case OrderStep.SELECT_DROPOFF:
                this.currentStep.set(OrderStep.SELECT_PICKUP);
                break;
            case OrderStep.CONFIRM_ORDER:
                this.currentStep.set(OrderStep.SELECT_DROPOFF);
                break;
            default:
                break;
        }
    }
}
