.trip-card {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.trip-header {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: white;
    padding: 25px 20px;
    position: relative;
    overflow: hidden;
}

.trip-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.trip-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.trip-title h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: white;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 0;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.en-route {
    background: #ffffff;
    color: #000000;
}

.trip-meta {
    color: #cccccc;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.trip-progress {
    padding: 30px 20px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.trip-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20px;
    right: 20px;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #ffffff;
    border: 3px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-text {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    margin-bottom: 4px;
}

.step-timestamp {
    font-size: 10px;
    color: #999;
}

.progress-step.active .step-icon {
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
    color: #000000;
    border-color: #000000;
    box-shadow:
        0 0 0 3px #ffffff,
        0 0 0 6px #000000,
        0 4px 12px rgba(0, 0, 0, 0.15);
    animation: pulse 2s infinite;
    transform: scale(1.15);
}

.progress-step.active .step-text {
    color: #000000;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.trip-stats {
    padding: 25px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #fafafa;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    background: #ffffff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 24px;
    margin-right: 12px;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.route-info {
    padding: 25px 20px;
    background: #ffffff;
    position: relative;
}

.route-point {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.route-point:last-child {
    margin-bottom: 0;
}

.point-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 15px;
    flex-shrink: 0;
}

.route-point.pickup .point-icon {
    background: #e8f5e8;
}

.route-point.dropoff .point-icon {
    background: #fff3e0;
}

.point-details {
    flex: 1;
}

.point-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.point-address {
    font-size: 14px;
    color: #000;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.point-time {
    font-size: 12px;
    color: #666;
}

.route-line {
    position: absolute;
    left: 39px;
    top: 65px;
    bottom: 65px;
    width: 2px;
    background: linear-gradient(to bottom, #e8f5e8, #fff3e0);
    z-index: 1;
}

.status-message {
    padding: 25px 20px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.status-message.en-route {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-left: 4px solid #4caf50;
}

.status-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.status-text h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.status-text p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.5;
}

.location-info {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: rgba(255, 255, 255, 0.7);
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
}

.action-buttons {
    padding: 25px 20px;
    background: #ffffff;
    display: flex;
    gap: 15px;
    border-top: 1px solid #e0e0e0;
}

.action-btn {
    flex: 1;
    height: 50px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-btn.primary {
    background: #000000;
    color: white;
    border-color: #000000;
}

.action-btn.primary:hover {
    background: #333333;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.action-btn.secondary {
    background: #ffffff;
    color: #000000;
    border-color: #000000;
}

.action-btn.secondary:hover {
    background: #f8f8f8;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
