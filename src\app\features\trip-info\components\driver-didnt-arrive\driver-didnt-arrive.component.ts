import { Component, Input, Signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Router } from '@angular/router';
import { TripStatusComponentData } from '../base-trip-status.interface';
import { TripStatus } from '../../../../core/types/tripoos.types';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
    selector: 'app-driver-didnt-arrive',
    standalone: true,
    imports: [CommonModule, ButtonModule, ProgressSpinnerModule],
    templateUrl: './driver-didnt-arrive.component.html',
    styleUrls: ['./driver-didnt-arrive.component.scss']
})
export class DriverDidntArriveComponent {
    @Input({ required: true }) data!: TripStatusComponentData;
    
    private router = inject(Router);
    private authService = inject(AuthService);
    
    TripStatus = TripStatus;
    
    isDriver = computed(() => {
        const user = this.authService.currentUser();
        return user?.driverStatus === 'APPROVED';
    });
    
    formatDate(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    }
    
    formatTime(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }
    
    formatDuration(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }
    
    getStatusClass(): string {
        return 'en-route';
    }
    
    getStatusText(): string {
        return 'En Route';
    }
    
    getStatusTitle(): string {
        return this.isDriver() 
            ? 'Navigate to pickup location' 
            : 'Driver is on the way';
    }
    
    getStatusDescription(): string {
        return this.isDriver()
            ? 'Please navigate to the pickup location and confirm your arrival.'
            : 'Your driver is currently en route to the pickup location.';
    }
    
    onUpdateLocation(): void {
        this.data.onUpdateLocation();
    }
    
    onGoBack(): void {
        this.data.onGoBack();
    }
    
    onConfirmArrival(): void {
        if (this.data.onActionComplete) {
            this.data.onActionComplete('confirm_arrival');
        }
    }
}
