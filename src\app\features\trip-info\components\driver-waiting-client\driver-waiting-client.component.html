<div class="driver-waiting-container">
    <!-- Minimalist Header -->
    <div class="minimalist-header">
        <h2>Order Details</h2>
        <div class="status-badge" [ngClass]="'Waiting'">
            {{ "Waiting" }}
        </div>
    </div>

    <!-- Order Summary Card -->
    <div class="order-summary-card">
        <div class="order-header">
            <div class="order-id">
                Order #{{ data.trip.id.substring(0, 8) }}
            </div>
            <div class="order-date">{{ formatDate(data.trip.createdAt) }}</div>
        </div>

        <!-- Route Information -->
        <div class="route-section">
            <div class="route-item pickup">
                <div class="route-icon">📍</div>
                <div class="route-details">
                    <div class="route-label">Pickup Location</div>
                    <div class="route-address">{{ data.pickupAddress }}</div>
                    <div class="route-time">
                        {{
                            data.estimatedPickupTime
                                ? formatTime(data.estimatedPickupTime)
                                : formatTime(data.order.createdAt)
                        }}
                    </div>
                </div>
            </div>

            <div class="route-divider"></div>

            <div class="route-item dropoff">
                <div class="route-icon">🎯</div>
                <div class="route-details">
                    <div class="route-label">Dropoff Location</div>
                    <div class="route-address">{{ data.dropoffAddress }}</div>
                    <div class="route-time">
                        {{
                            data.estimatedDropoffTime
                                ? formatTime(data.estimatedDropoffTime)
                                : "Estimated"
                        }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Trip Details -->
        <div class="trip-details">
            <div class="detail-item">
                <span class="detail-label">Distance:</span>
                <span class="detail-value"
                    >{{ data.estimatedDistance || "TBD" }}km</span
                >
            </div>
            <div class="detail-item">
                <span class="detail-label">Duration:</span>
                <span class="detail-value">{{
                    data.estimatedDuration
                        ? formatDuration(data.estimatedDuration)
                        : "TBD"
                }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Fare:</span>
                <span class="detail-value"
                    >${{
                        data.order.initialPrice ||
                            data.order.finalPrice ||
                            "TBD"
                    }}</span
                >
            </div>
        </div>
    </div>

    <!-- Status Message -->
    <div class="status-section">
        <div class="status-icon">⏰</div>
        <div class="status-content">
            <h3>
                {{
                    this.isDriver()
                        ? "Waiting for client"
                        : "Driver has arrived"
                }}
            </h3>
            <p>
                {{
                    this.isDriver()
                        ? "Please wait for the client to arrive at the pickup location."
                        : "Your driver has arrived at the pickup location and is waiting for you."
                }}
            </p>
            @if (
                data.trip.currentLocationLatitude &&
                data.trip.currentLocationLongitude &&
                isDriver()
            ) {
                <p class="location-info">
                    📍 Current location:
                    {{ data.trip.currentLocationLatitude!.toFixed(6) }},
                    {{ data.trip.currentLocationLongitude!.toFixed(6) }}
                </p>
            }
        </div>
    </div>

    <!-- Action Section for Driver -->
    @if (isDriver()) {
        <div class="action-section">
            <!-- Swipe to Start Trip -->
            <div class="swipe-container">
                <div
                    class="swipe-track"
                    (touchstart)="onSwipeStart()"
                    (mousedown)="onSwipeStart()"
                >
                    <div
                        class="swipe-thumb"
                        [style.transform]="
                            'translateX(' + swipeProgress() + 'px)'
                        "
                    >
                        @if (!isSwipeLoading()) {
                            <span>👆</span>
                        } @else {
                            <div class="swipe-loading">
                                <p-progressSpinner
                                    [style]="{
                                        width: '20px',
                                        height: '20px',
                                    }"
                                ></p-progressSpinner>
                            </div>
                        }
                    </div>
                    <div class="swipe-text">
                        @if (!isSwipeLoading()) {
                            Swipe to start trip →
                        } @else {
                            Starting trip...
                        }
                    </div>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="additional-actions">
                <button
                    pButton
                    label="📍 Update Location"
                    icon="pi pi-refresh"
                    class="action-btn secondary"
                    [loading]="data.isUpdatingLocation"
                    (click)="onUpdateLocation()"
                ></button>
            </div>
        </div>
    }

    <!-- Last Updated -->
    @if (data.lastUpdated) {
        <div class="last-updated">
            Last updated: {{ formatTime(data.lastUpdated) }}
        </div>
    }
</div>
