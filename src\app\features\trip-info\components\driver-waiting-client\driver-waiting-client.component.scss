.driver-waiting-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.minimalist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 0 5px;
}

.minimalist-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #000000;
    letter-spacing: -0.5px;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 0;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid #000000;
}

.status-badge.waiting {
    background: #f0f0f0;
    color: #000000;
}

.order-summary-card {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.order-header {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-id {
    font-size: 18px;
    font-weight: 600;
}

.order-date {
    font-size: 14px;
    color: #cccccc;
}

.route-section {
    padding: 25px 20px;
    background: #ffffff;
}

.route-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.route-item:last-child {
    margin-bottom: 0;
}

.route-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 15px;
    flex-shrink: 0;
}

.route-item.pickup .route-icon {
    background: #e8f5e8;
}

.route-item.dropoff .route-icon {
    background: #fff3e0;
}

.route-details {
    flex: 1;
}

.route-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.route-address {
    font-size: 14px;
    color: #000;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.route-time {
    font-size: 12px;
    color: #666;
}

.route-divider {
    height: 20px;
    width: 2px;
    background: linear-gradient(to bottom, #e8f5e8, #fff3e0);
    margin: 0 auto 20px auto;
    margin-left: 39px;
}

.trip-details {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.detail-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.detail-value {
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.status-section {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.status-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.status-content h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #000;
}

.status-content p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.5;
}

.location-info {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
}

.action-section {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.swipe-container {
    margin-bottom: 20px;
}

.swipe-track {
    position: relative;
    height: 60px;
    background: #f0f0f0;
    border: 2px solid #000000;
    border-radius: 30px;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
}

.swipe-thumb {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 50px;
    height: 50px;
    background: #000000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: transform 0.3s ease;
    z-index: 2;
}

.swipe-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: 600;
    color: #666;
    pointer-events: none;
    z-index: 1;
}

.swipe-loading {
    display: flex;
    align-items: center;
    justify-content: center;
}

.additional-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    flex: 1;
    height: 50px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-btn.secondary {
    background: #ffffff;
    color: #000000;
    border-color: #000000;
}

.action-btn.secondary:hover {
    background: #f8f8f8;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.last-updated {
    text-align: center;
    font-size: 12px;
    color: #666;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    margin-top: 20px;
}
