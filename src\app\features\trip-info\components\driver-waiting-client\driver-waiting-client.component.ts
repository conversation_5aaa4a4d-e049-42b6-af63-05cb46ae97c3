import {
    Component,
    Input,
    Signal,
    computed,
    inject,
    signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Router } from '@angular/router';
import { TripStatusComponentData } from '../base-trip-status.interface';
import { TripStatus } from '../../../../core/types/tripoos.types';
import { AuthService } from '../../../../services/auth.service';

@Component({
    selector: 'app-driver-waiting-client',
    standalone: true,
    imports: [CommonModule, ButtonModule, ProgressSpinnerModule],
    templateUrl: './driver-waiting-client.component.html',
    styleUrls: ['./driver-waiting-client.component.scss'],
})
export class DriverWaitingClientComponent {
    @Input({ required: true }) data!: TripStatusComponentData;

    private router = inject(Router);
    private authService = inject(AuthService);

    TripStatus = TripStatus;

    // Swipe functionality
    isSwipeLoading = signal(false);
    swipeProgress = signal(0);

    isDriver = computed(() => {
        const user = this.authService.user();
        return user?.driverStatus === 'APPROVED';
    });

    formatDate(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    }

    formatTime(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }

    formatDuration(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }

    onUpdateLocation(): void {
        this.data.onUpdateLocation();
    }

    onGoBack(): void {
        this.data.onGoBack();
    }

    onSwipeStart(): void {
        if (this.isDriver()) {
            this.isSwipeLoading.set(true);
            if (this.data.onActionComplete) {
                this.data.onActionComplete('start_trip');
            }
        }
    }

    onSwipeProgress(progress: number): void {
        this.swipeProgress.set(progress);
    }

    onSwipeComplete(): void {
        this.isSwipeLoading.set(false);
        this.swipeProgress.set(0);
    }
}
