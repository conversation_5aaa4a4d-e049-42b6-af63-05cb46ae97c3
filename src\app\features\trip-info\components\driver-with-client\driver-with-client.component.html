<div class="trip-card">
    <!-- Header -->
    <div class="trip-header">
        <div class="trip-title">
            <h1>Trip #{{ data.trip.id.substring(0, 8) }}</h1>
            <div class="status-badge" [ngClass]="getStatusClass()">
                {{ getStatusText() }}
            </div>
        </div>
        <div class="trip-meta">
            <span>{{ formatDate(data.trip.createdAt) }}</span>
        </div>
    </div>

    <!-- Trip Progress -->
    <div class="trip-progress">
        <div class="progress-step completed">
            <div class="step-icon">🚗</div>
            <div class="step-text">En Route</div>
            <div class="step-timestamp">
                {{ formatTime(data.trip.createdAt) }}
            </div>
        </div>
        <div class="progress-step completed">
            <div class="step-icon">⏰</div>
            <div class="step-text">Waiting</div>
        </div>
        <div class="progress-step active">
            <div class="step-icon">🛣️</div>
            <div class="step-text">In Progress</div>
        </div>
        <div class="progress-step">
            <div class="step-icon">✅</div>
            <div class="step-text">Completed</div>
        </div>
    </div>

    <!-- Trip Stats -->
    <div class="trip-stats">
        <div class="stat-item">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
                <div class="stat-label">Fare</div>
                <div class="stat-value">
                    ${{
                        data.order.initialPrice ||
                            data.order.finalPrice ||
                            "TBD"
                    }}
                </div>
            </div>
        </div>
        <div class="stat-item">
            <div class="stat-icon">📏</div>
            <div class="stat-content">
                <div class="stat-label">Distance</div>
                <div class="stat-value">
                    {{ data.estimatedDistance || "TBD" }}km
                </div>
            </div>
        </div>
        <div class="stat-item">
            <div class="stat-icon">⏱️</div>
            <div class="stat-content">
                <div class="stat-label">Duration</div>
                <div class="stat-value">
                    {{
                        data.estimatedDuration
                            ? formatDuration(data.estimatedDuration)
                            : "TBD"
                    }}
                </div>
            </div>
        </div>
    </div>

    <!-- Route Information -->
    <div class="route-info">
        <div class="route-point pickup completed">
            <div class="point-icon">✅</div>
            <div class="point-details">
                <div class="point-label">Pickup</div>
                <div class="point-address">{{ data.pickupAddress }}</div>
                <div class="point-time">
                    {{
                        data.estimatedPickupTime
                            ? formatTime(data.estimatedPickupTime)
                            : formatTime(data.order.createdAt)
                    }}
                </div>
            </div>
        </div>
        <div class="route-line active"></div>
        <div class="route-point dropoff">
            <div class="point-icon">🎯</div>
            <div class="point-details">
                <div class="point-label">Dropoff</div>
                <div class="point-address">{{ data.dropoffAddress }}</div>
                <div class="point-time">
                    {{
                        data.estimatedDropoffTime
                            ? formatTime(data.estimatedDropoffTime)
                            : "Estimated"
                    }}
                </div>
            </div>
        </div>
    </div>

    <!-- Current Status Message -->
    <div class="status-message in-progress">
        <div class="status-content">
            @if (data.isActionInProgress) {
                <p-progressSpinner
                    strokeWidth="2"
                    [style]="{
                        width: '20px',
                        height: '20px',
                    }"
                ></p-progressSpinner>
            }
            <div class="status-text">
                <h3>{{ getStatusTitle() }}</h3>
                <p>{{ getStatusDescription() }}</p>
                @if (
                    data.trip.currentLocationLatitude &&
                    data.trip.currentLocationLongitude &&
                    isDriver()
                ) {
                    <p class="location-info">
                        📍 Current location:
                        {{ data.trip.currentLocationLatitude!.toFixed(6) }},
                        {{ data.trip.currentLocationLongitude!.toFixed(6) }}
                    </p>
                }
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    @if (isDriver()) {
        <div class="action-buttons">
            <button
                pButton
                label="🏁 Complete Trip"
                icon="pi pi-check"
                class="action-btn primary"
                [loading]="data.isActionInProgress"
                (click)="onCompleteTrip()"
            ></button>
            <button
                pButton
                label="📍 Update Location"
                icon="pi pi-refresh"
                class="action-btn secondary"
                [loading]="data.isUpdatingLocation"
                (click)="onUpdateLocation()"
            ></button>
        </div>
    }

    <!-- Last Updated -->
    @if (data.lastUpdated) {
        <div class="last-updated">
            Last updated: {{ formatTime(data.lastUpdated) }}
        </div>
    }
</div>
