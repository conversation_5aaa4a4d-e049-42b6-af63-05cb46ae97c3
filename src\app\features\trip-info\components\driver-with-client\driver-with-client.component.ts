import { Component, Input, Signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Router } from '@angular/router';
import { TripStatusComponentData } from '../base-trip-status.interface';
import { TripStatus } from '../../../../core/types/tripoos.types';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
    selector: 'app-driver-with-client',
    standalone: true,
    imports: [CommonModule, ButtonModule, ProgressSpinnerModule],
    templateUrl: './driver-with-client.component.html',
    styleUrls: ['./driver-with-client.component.scss']
})
export class DriverWithClientComponent {
    @Input({ required: true }) data!: TripStatusComponentData;
    
    private router = inject(Router);
    private authService = inject(AuthService);
    
    TripStatus = TripStatus;
    
    isDriver = computed(() => {
        const user = this.authService.currentUser();
        return user?.driverStatus === 'APPROVED';
    });
    
    formatDate(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    }
    
    formatTime(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }
    
    formatDuration(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }
    
    getStatusClass(): string {
        return 'in-progress';
    }
    
    getStatusText(): string {
        return 'In Progress';
    }
    
    getStatusTitle(): string {
        return 'Trip in progress';
    }
    
    getStatusDescription(): string {
        return this.isDriver()
            ? 'You are currently driving the client to their destination.'
            : 'You are currently on your way to the destination.';
    }
    
    onUpdateLocation(): void {
        this.data.onUpdateLocation();
    }
    
    onGoBack(): void {
        this.data.onGoBack();
    }
    
    onCompleteTrip(): void {
        if (this.data.onActionComplete) {
            this.data.onActionComplete('complete_trip');
        }
    }
}
