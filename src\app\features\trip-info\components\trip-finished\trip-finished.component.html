<div class="trip-card">
    <!-- Header -->
    <div class="trip-header">
        <div class="completion-icon">✅</div>
        <div class="trip-title">
            <h1>Trip Completed</h1>
            <div class="status-badge" [ngClass]="getStatusClass()">
                {{ getStatusText() }}
            </div>
        </div>
        <div class="trip-meta">
            <span>Trip #{{ data.trip.id.substring(0, 8) }}</span>
            <span>{{ formatDate(data.trip.createdAt) }}</span>
        </div>
    </div>

    <!-- Trip Progress -->
    <div class="trip-progress">
        <div class="progress-step completed">
            <div class="step-icon">🚗</div>
            <div class="step-text">En Route</div>
            <div class="step-timestamp">
                {{ formatTime(data.trip.createdAt) }}
            </div>
        </div>
        <div class="progress-step completed">
            <div class="step-icon">⏰</div>
            <div class="step-text">Waiting</div>
        </div>
        <div class="progress-step completed">
            <div class="step-icon">🛣️</div>
            <div class="step-text">In Progress</div>
        </div>
        <div class="progress-step completed">
            <div class="step-icon">✅</div>
            <div class="step-text">Completed</div>
        </div>
    </div>

    <!-- Trip Summary -->
    <div class="trip-summary">
        <h3>Trip Summary</h3>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-label">Total Fare</div>
                    <div class="stat-value">
                        ${{
                            data.order.finalPrice ||
                                data.order.initialPrice ||
                                "TBD"
                        }}
                    </div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">📏</div>
                <div class="stat-content">
                    <div class="stat-label">Distance</div>
                    <div class="stat-value">
                        {{ data.estimatedDistance || "TBD" }}km
                    </div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">⏱️</div>
                <div class="stat-content">
                    <div class="stat-label">Duration</div>
                    <div class="stat-value">
                        {{
                            data.estimatedDuration
                                ? formatDuration(data.estimatedDuration)
                                : "TBD"
                        }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Route Information -->
    <div class="route-info">
        <div class="route-point pickup completed">
            <div class="point-icon">✅</div>
            <div class="point-details">
                <div class="point-label">Pickup</div>
                <div class="point-address">{{ data.pickupAddress }}</div>
                <div class="point-time">
                    {{
                        data.estimatedPickupTime
                            ? formatTime(data.estimatedPickupTime)
                            : formatTime(data.order.createdAt)
                    }}
                </div>
            </div>
        </div>
        <div class="route-line completed"></div>
        <div class="route-point dropoff completed">
            <div class="point-icon">✅</div>
            <div class="point-details">
                <div class="point-label">Dropoff</div>
                <div class="point-address">{{ data.dropoffAddress }}</div>
                <div class="point-time">
                    {{
                        data.estimatedDropoffTime
                            ? formatTime(data.estimatedDropoffTime)
                            : "Completed"
                    }}
                </div>
            </div>
        </div>
    </div>

    <!-- Completion Message -->
    <div class="completion-message">
        <div class="message-content">
            <div class="message-icon">🎉</div>
            <div class="message-text">
                <h3>{{ getStatusTitle() }}</h3>
                <p>{{ getStatusDescription() }}</p>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        @if (!isDriver()) {
            <button
                pButton
                label="⭐ Rate Trip"
                icon="pi pi-star"
                class="action-btn primary"
                (click)="onRateTrip()"
            ></button>
            <button
                pButton
                label="🚗 Book Another"
                icon="pi pi-plus"
                class="action-btn secondary"
                (click)="onBookAnother()"
            ></button>
        } @else {
            <button
                pButton
                label="📄 View Receipt"
                icon="pi pi-file"
                class="action-btn secondary"
                (click)="onViewReceipt()"
            ></button>
        }
        <button
            pButton
            label="← Go Back"
            icon="pi pi-arrow-left"
            class="action-btn tertiary"
            (click)="onGoBack()"
        ></button>
    </div>

    <!-- Trip Details Footer -->
    <div class="trip-footer">
        <div class="footer-item">
            <span class="footer-label">Trip ID:</span>
            <span class="footer-value">{{ data.trip.id }}</span>
        </div>
        <div class="footer-item">
            <span class="footer-label">Completed:</span>
            <span class="footer-value"
                >{{ formatDate(data.trip.createdAt) }} at
                {{ formatTime(data.trip.createdAt) }}</span
            >
        </div>
    </div>
</div>
