.trip-card {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.trip-header {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    color: white;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.trip-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.completion-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: bounce 1s ease-in-out;
    position: relative;
    z-index: 1;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.trip-title {
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.trip-title h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
    color: white;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-badge.completed {
    background: #ffffff;
    color: #4caf50;
}

.trip-meta {
    color: #e8f5e8;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.trip-meta span {
    display: block;
    margin-bottom: 5px;
}

.trip-progress {
    padding: 30px 20px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.trip-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20px;
    right: 20px;
    height: 3px;
    background: #4caf50;
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #4caf50;
    color: white;
    border: 3px solid #4caf50;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-text {
    font-size: 12px;
    font-weight: 600;
    color: #4caf50;
    margin-bottom: 4px;
}

.step-timestamp {
    font-size: 10px;
    color: #999;
}

.trip-summary {
    padding: 25px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.trip-summary h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #000;
    text-align: center;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    background: #ffffff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 24px;
    margin-right: 12px;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.route-info {
    padding: 25px 20px;
    background: #ffffff;
    position: relative;
}

.route-point {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.route-point:last-child {
    margin-bottom: 0;
}

.point-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4caf50;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 15px;
    flex-shrink: 0;
}

.point-details {
    flex: 1;
}

.point-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.point-address {
    font-size: 14px;
    color: #000;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.point-time {
    font-size: 12px;
    color: #666;
}

.route-line {
    position: absolute;
    left: 39px;
    top: 65px;
    bottom: 65px;
    width: 3px;
    background: #4caf50;
    z-index: 1;
}

.completion-message {
    padding: 25px 20px;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%);
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
}

.message-content {
    display: flex;
    align-items: center;
    gap: 20px;
    text-align: left;
}

.message-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.message-text h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #000;
}

.message-text p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

.action-buttons {
    padding: 25px 20px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-top: 1px solid #e0e0e0;
}

.action-btn {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-btn.primary {
    background: #4caf50;
    color: white;
    border-color: #4caf50;
}

.action-btn.primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.action-btn.secondary {
    background: #ffffff;
    color: #000000;
    border-color: #000000;
}

.action-btn.secondary:hover {
    background: #f8f8f8;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.action-btn.tertiary {
    background: #f8f9fa;
    color: #666;
    border-color: #e0e0e0;
}

.action-btn.tertiary:hover {
    background: #e9ecef;
    color: #333;
}

.trip-footer {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.footer-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.footer-item:last-child {
    margin-bottom: 0;
}

.footer-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.footer-value {
    font-size: 12px;
    color: #000;
    font-weight: 600;
}
