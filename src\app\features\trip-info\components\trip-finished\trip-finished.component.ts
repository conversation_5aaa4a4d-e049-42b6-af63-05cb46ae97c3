import { Component, Input, Signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Router } from '@angular/router';
import { TripStatusComponentData } from '../base-trip-status.interface';
import { TripStatus } from '../../../../core/types/tripoos.types';
import { AuthService } from '../../../../services/auth.service';

@Component({
    selector: 'app-trip-finished',
    standalone: true,
    imports: [CommonModule, ButtonModule, ProgressSpinnerModule],
    templateUrl: './trip-finished.component.html',
    styleUrls: ['./trip-finished.component.scss'],
})
export class TripFinishedComponent {
    @Input({ required: true }) data!: TripStatusComponentData;

    private router = inject(Router);
    private authService = inject(AuthService);

    TripStatus = TripStatus;

    isDriver = computed(() => {
        const user = this.authService.user();
        return user?.driverStatus === 'APPROVED';
    });

    formatDate(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    }

    formatTime(date: string | Date): string {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }

    formatDuration(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }

    getStatusClass(): string {
        return 'completed';
    }

    getStatusText(): string {
        return 'Completed';
    }

    getStatusTitle(): string {
        return 'Trip completed successfully';
    }

    getStatusDescription(): string {
        return this.isDriver()
            ? 'You have successfully completed this trip. Thank you for your service!'
            : 'Your trip has been completed successfully. Thank you for choosing our service!';
    }

    onGoBack(): void {
        this.data.onGoBack();
    }

    onViewReceipt(): void {
        // Navigate to receipt or trip details
        this.router.navigate(['/trips', this.data.trip.id, 'receipt']);
    }

    onBookAnother(): void {
        // Navigate to booking page
        this.router.navigate(['/book']);
    }

    onRateTrip(): void {
        // Navigate to rating page or open rating modal
        if (this.data.onActionComplete) {
            this.data.onActionComplete('rate_trip');
        }
    }
}
