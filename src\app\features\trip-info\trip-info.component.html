<div class="trip-info-container">
    @if (isLoading() && !trip()) {
        <div class="loading-state">
            <p-progressSpinner
                strokeWidth="2"
                [style]="{ width: '50px', height: '50px' }"
            ></p-progressSpinner>
            <p>Loading trip information...</p>
        </div>
    } @else if (trip() && order()) {
        <!-- Special UI for DRIVER_DIDNT_ARRIVE and DRIVER_WAITING_CLIENT status -->
        @if (
            trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE ||
            trip()!.status === TripStatus.DRIVER_WAITING_CLIENT
        ) {
            <div class="driver-didnt-arrive-container">
                <!-- Minimalist Header -->
                <div class="minimalist-header">
                    <h2>Order Details</h2>
                    <div class="status-badge" [ngClass]="getStatusClass()">
                        {{ getStatusText() }}
                    </div>
                </div>

                <!-- Map Section -->
                <div class="map-section flex flex-col">
                    <app-map
                        [nodes]="mapNodes()"
                        [options]="{
                            center: mapCenter(),
                            zoom: mapZoom(),
                        }"
                        [showLayersControl]="false"
                    >
                    </app-map>
                </div>

                <!-- Trip Info (Minimalist) -->
                <div class="trip-info-minimal">
                    <div class="info-row">
                        <span class="label">📍 Pickup:</span>
                        <span class="value">
                            @if (pickupAddressLoading()) {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            } @else if (pickupAddress()) {
                                {{ pickupAddress() }}
                            } @else {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            }
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">🏁 Destination:</span>
                        <span class="value">
                            @if (dropoffAddressLoading()) {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            } @else if (dropoffAddress()) {
                                {{ dropoffAddress() }}
                            } @else {
                                <div class="skeleton-loader">
                                    <div class="skeleton-line"></div>
                                </div>
                            }
                        </span>
                    </div>
                    @if (isDriver()) {
                        <div class="info-row">
                            <span class="label">👤 Client:</span>
                            <span class="value"
                                >{{ order()!.user?.firstName }}
                                {{ order()!.user?.lastName }}</span
                            >
                        </div>
                    } @else {
                        <div class="info-row">
                            <span class="label">🚗 Driver:</span>
                            <span class="value"
                                >{{ trip()!.driver?.firstName }}
                                {{ trip()!.driver?.lastName }}</span
                            >
                        </div>
                    }
                </div>

                <!-- Action Buttons (Driver Only) -->
                @if (isDriver()) {
                    <div class="action-buttons-minimal">
                        @if (
                            trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE
                        ) {
                            <!-- Arrive Swipe Button -->
                            <div
                                class="swipe-container"
                                [class.swipe-completed]="isSwipeCompleted()"
                                (mousedown)="startSlide($event)"
                                (touchstart)="startSlide($event)"
                                (mousemove)="onSlide($event)"
                                (touchmove)="onSlide($event)"
                                (mouseup)="endSlide($event)"
                                (touchend)="endSlide($event)"
                                (mouseleave)="endSlide($event)"
                            >
                                <div class="swipe-track">
                                    <div
                                        class="swipe-progress"
                                        [style.width.%]="getSwipeProgress()"
                                    ></div>
                                    <div class="swipe-target">
                                        <span class="target-text">Arrive</span>
                                        <span class="target-icon">📍</span>
                                    </div>
                                </div>
                                <div
                                    class="swipe-handle"
                                    [style.left.%]="getHandlePosition()"
                                    [class.swipe-active]="isSliding()"
                                >
                                    <span class="handle-icon">🚗</span>
                                </div>
                                @if (isActionInProgress()) {
                                    <div class="swipe-loading">
                                        <p-progressSpinner
                                            [style]="{
                                                width: '20px',
                                                height: '20px',
                                            }"
                                        ></p-progressSpinner>
                                    </div>
                                }
                            </div>
                        } @else if (
                            trip()!.status === TripStatus.DRIVER_WAITING_CLIENT
                        ) {
                            <!-- Client Packed Swipe Button -->
                            <div
                                class="swipe-container"
                                [class.swipe-completed]="isSwipeCompleted()"
                                (mousedown)="startSlide($event)"
                                (touchstart)="startSlide($event)"
                                (mousemove)="onSlide($event)"
                                (touchmove)="onSlide($event)"
                                (mouseup)="endSlide($event)"
                                (touchend)="endSlide($event)"
                                (mouseleave)="endSlide($event)"
                            >
                                <div class="swipe-track">
                                    <div
                                        class="swipe-progress"
                                        [style.width.%]="getSwipeProgress()"
                                    ></div>
                                    <div class="swipe-target">
                                        <span class="target-text"
                                            >Client Packed</span
                                        >
                                    </div>
                                </div>
                                <div
                                    class="swipe-handle"
                                    [style.left.%]="getHandlePosition()"
                                    [class.swipe-active]="isSliding()"
                                >
                                    <span class="handle-icon">👤</span>
                                </div>
                                @if (isActionInProgress()) {
                                    <div class="swipe-loading">
                                        <p-progressSpinner
                                            [style]="{
                                                width: '20px',
                                                height: '20px',
                                            }"
                                        ></p-progressSpinner>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }
            </div>
        } @else {
            <!-- Regular Trip UI for other statuses -->
            <div class="trip-card">
                <!-- Header -->
                <div class="trip-header">
                    <div class="trip-title">
                        <h1>Trip #{{ trip()!.id.substring(0, 8) }}</h1>
                        <div class="status-badge" [ngClass]="getStatusClass()">
                            {{ getStatusText() }}
                        </div>
                    </div>
                    <div class="trip-meta">
                        <span>{{ formatDate(trip()!.createdAt) }}</span>
                    </div>
                </div>

                <!-- Trip Progress -->
                <div class="trip-progress">
                    <div class="progress-header">
                        <h2>Trip Progress</h2>
                        <p-progressBar
                            [value]="getProgressPercentage()"
                            [showValue]="true"
                            styleClass="custom-progress-bar"
                        >
                        </p-progressBar>
                    </div>
                    <div class="progress-steps">
                        @for (step of progressSteps(); track step.key) {
                            <div
                                class="progress-step"
                                [ngClass]="step.className"
                            >
                                <div class="step-icon">
                                    {{ step.icon }}
                                </div>
                                <div class="step-text">
                                    {{ step.text }}
                                </div>
                                @if (step.timestamp) {
                                    <div class="step-time">
                                        {{ formatTime(step.timestamp) }}
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>

                <!-- Trip Stats -->
                <div class="trip-stats">
                    <div class="stat-item">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-details">
                            <h4>Duration</h4>
                            <p>{{ getTripDuration() }}</p>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">📏</div>
                        <div class="stat-details">
                            <h4>Distance</h4>
                            <p>{{ getEstimatedDistance() }}</p>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">💰</div>
                        <div class="stat-details">
                            <h4>Fare</h4>
                            <p>{{ getEstimatedFare() }}</p>
                        </div>
                    </div>
                </div>

                <!-- Route Information -->
                <div class="route-info">
                    <div class="location-item pickup">
                        <div class="location-icon">📍</div>
                        <div class="location-details">
                            <h3>Pickup Location</h3>
                            <p>
                                @if (pickupAddressLoading()) {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                } @else if (pickupAddress()) {
                                    {{ pickupAddress() }}
                                } @else {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                }
                            </p>
                        </div>
                    </div>
                    <div class="route-line">
                        <div
                            class="route-progress"
                            [style.height.%]="getProgressPercentage()"
                        ></div>
                    </div>
                    <div class="location-item dropoff">
                        <div class="location-icon">🏁</div>
                        <div class="location-details">
                            <h3>Destination</h3>
                            <p>
                                @if (dropoffAddressLoading()) {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                } @else if (dropoffAddress()) {
                                    {{ dropoffAddress() }}
                                } @else {
                                    <div class="skeleton-loader">
                                        <div class="skeleton-line"></div>
                                    </div>
                                }
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Participants -->
                <div class="participants">
                    @if (isDriver()) {
                        <!-- Show client info to driver -->
                        <div class="participant client-card">
                            <div class="participant-avatar">
                                <div class="avatar-icon">👤</div>
                            </div>
                            <div class="participant-details">
                                <h3>Client</h3>
                                <p class="name">
                                    {{ order()!.user?.firstName }}
                                    {{ order()!.user?.lastName }}
                                </p>
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Client'"
                                        (click)="callClient()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Client'"
                                        (click)="messageClient()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    } @else {
                        <!-- Show driver info to client -->
                        <div class="participant driver-card">
                            <div class="participant-avatar">
                                <div class="avatar-icon">🚗</div>
                            </div>
                            <div class="participant-details">
                                <h3>Driver</h3>
                                <p class="name">
                                    {{ trip()!.driver?.firstName }}
                                    {{ trip()!.driver?.lastName }}
                                </p>
                                @if (trip()!.driver?.car) {
                                    <div class="car-details">
                                        <p class="car-info">
                                            {{ trip()!.driver?.car?.make }}
                                            {{ trip()!.driver?.car?.model }}
                                        </p>
                                        <p class="car-year">
                                            ({{ trip()!.driver?.car?.year }})
                                        </p>
                                        @if (
                                            trip()!.driver?.car?.licensePlate
                                        ) {
                                            <div class="license-plate">
                                                {{
                                                    trip()!.driver?.car
                                                        ?.licensePlate
                                                }}
                                            </div>
                                        }
                                    </div>
                                }
                                <div class="contact-actions">
                                    <button
                                        pButton
                                        icon="pi pi-phone"
                                        class="contact-btn"
                                        [pTooltip]="'Call Driver'"
                                        (click)="callDriver()"
                                    ></button>
                                    <button
                                        pButton
                                        icon="pi pi-comment"
                                        class="contact-btn"
                                        [pTooltip]="'Message Driver'"
                                        (click)="messageDriver()"
                                    ></button>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Current Status Message -->
                <div class="status-message" [ngClass]="getStatusMessageClass()">
                    <div class="status-content">
                        @if (isActionInProgress()) {
                            <p-progressSpinner
                                strokeWidth="2"
                                [style]="{
                                    width: '20px',
                                    height: '20px',
                                }"
                            ></p-progressSpinner>
                        }
                        <div class="status-text">
                            <h3>{{ getStatusTitle() }}</h3>
                            <p>{{ getStatusDescription() }}</p>
                            @if (
                                trip()!.currentLocationLatitude &&
                                trip()!.currentLocationLongitude &&
                                isDriver()
                            ) {
                                <p class="location-info">
                                    📍 Current location:
                                    {{
                                        trip()!.currentLocationLatitude!.toFixed(
                                            6
                                        )
                                    }},
                                    {{
                                        trip()!.currentLocationLongitude!.toFixed(
                                            6
                                        )
                                    }}
                                </p>
                            }
                        </div>
                    </div>
                </div>

                <!-- Action Buttons (Driver Only) -->
                @if (isDriver() && canPerformAction()) {
                    <div class="action-buttons">
                        @switch (trip()!.status) {
                            @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                                <button
                                    pButton
                                    label="✓ Mark as Arrived"
                                    icon="pi pi-map-marker"
                                    class="action-btn primary"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmMarkArrived()"
                                ></button>
                                <button
                                    pButton
                                    label="📍 Update Location"
                                    icon="pi pi-refresh"
                                    class="action-btn secondary"
                                    [loading]="isUpdatingLocation()"
                                    (click)="updateLocation()"
                                ></button>
                            }
                            @case (TripStatus.DRIVER_WAITING_CLIENT) {
                                <button
                                    pButton
                                    label="🚀 Start Trip"
                                    icon="pi pi-play"
                                    class="action-btn primary pulse"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmStartTrip()"
                                ></button>
                            }
                            @case (TripStatus.DRIVER_WITH_CLIENT) {
                                <button
                                    pButton
                                    label="🏁 Complete Trip"
                                    icon="pi pi-check"
                                    class="action-btn primary"
                                    [loading]="isActionInProgress()"
                                    (click)="confirmCompleteTrip()"
                                ></button>
                                <button
                                    pButton
                                    label="📍 Update Location"
                                    icon="pi pi-refresh"
                                    class="action-btn secondary"
                                    [loading]="isUpdatingLocation()"
                                    (click)="updateLocation()"
                                ></button>
                            }
                        }
                    </div>
                }
            </div>
        }
    } @else {
        <div class="error-state">
            <h2>Trip Not Found</h2>
            <p>
                The requested trip could not be found or you don't have
                permission to view it.
            </p>
            <button
                pButton
                label="Go Back"
                class="action-btn secondary"
                (click)="goBack()"
            ></button>
        </div>
    }
</div>
