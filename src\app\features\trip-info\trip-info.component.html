<div class="trip-info-container">
    @if (isLoading() && !trip()) {
        <div class="loading-state">
            <p-progressSpinner
                strokeWidth="2"
                [style]="{ width: '50px', height: '50px' }"
            ></p-progressSpinner>
            <p>Loading trip information...</p>
        </div>
    } @else if (trip() && order() && componentData()) {
        <!-- Map Section (shared across all statuses) -->
        <div class="map-section">
            <app-map
                [nodes]="mapNodes()"
                [options]="{
                    center: mapCenter(),
                    zoom: mapZoom(),
                }"
                [showLayersControl]="false"
            >
            </app-map>
        </div>

        <!-- Use status-specific components -->
        @switch (trip()!.status) {
            @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                <app-driver-didnt-arrive [data]="componentData()!"></app-driver-didnt-arrive>
            }
            @case (TripStatus.DRIVER_WAITING_CLIENT) {
                <app-driver-waiting-client [data]="componentData()!"></app-driver-waiting-client>
            }
            @case (TripStatus.DRIVER_WITH_CLIENT) {
                <app-driver-with-client [data]="componentData()!"></app-driver-with-client>
            }
            @case (TripStatus.FINISHED) {
                <app-trip-finished [data]="componentData()!"></app-trip-finished>
            }
        }
    } @else {
        <div class="error-state">
            <h2>Trip Not Found</h2>
            <p>
                The requested trip could not be found or you don't have
                permission to view it.
            </p>
            <button
                pButton
                label="Go Back"
                class="action-btn secondary"
                (click)="goBack()"
            ></button>
        </div>
    }
</div>
