
            .trip-info-container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            .map-section {
                margin-bottom: 20px;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                border: 2px solid #000000;
                height: 300px;
            }

            .loading-state,
            .error-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                padding: 60px 20px;
                color: #333;
                animation: fadeIn 0.5s ease-in;
                background: #ffffff;
                border: 2px solid #000000;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                margin: 20px 0;
            }

            .error-state h2 {
                margin: 0 0 15px 0;
                font-size: 24px;
                font-weight: 600;
                color: #000;
            }

            .error-state p {
                margin: 0 0 25px 0;
                color: #666;
                line-height: 1.5;
            }

            .action-btn {
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
                border: 2px solid #000000;
                background: #ffffff;
                color: #000000;
                cursor: pointer;
            }

            .action-btn:hover {
                background: #f8f8f8;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .loading-state p,
            .error-state p {
                margin-top: 20px;
                color: #666;
            }

            .trip-card {
                background: #ffffff;
                border: 2px solid #000000;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                animation: slideInUp 0.6s ease-out;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                }
                to {
                    opacity: 1;
                }
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes pulse {
                0%,
                100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.05);
                }
            }

            .trip-header {
                background: linear-gradient(135deg, #000000 0%, #333333 100%);
                color: #ffffff;
                padding: 25px;
                position: relative;
                overflow: hidden;
            }

            .trip-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                    45deg,
                    transparent 48%,
                    rgba(255, 255, 255, 0.1) 50%,
                    transparent 52%
                );
                animation: shimmer 3s infinite;
            }

            @keyframes shimmer {
                0% {
                    transform: translateX(-100%);
                }
                100% {
                    transform: translateX(100%);
                }
            }

            .trip-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .trip-title h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
                color:white
            }

            .status-badge {
                padding: 6px 12px;
                border-radius: 0;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .status-badge.en-route {
                background: #ffffff;
                color: #000000;
            }

            .status-badge.waiting {
                background: #f0f0f0;
                color: #000000;
            }

            .status-badge.in-progress {
                background: #ffffff;
                color: #000000;
                animation: pulse 2s infinite;
            }

            .status-badge.completed {
                background: #f0f0f0;
                color: #000000;
            }

            @keyframes pulse {
                0%,
                100% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.7;
                }
            }

            .trip-meta {
                color: #cccccc;
                font-size: 14px;
            }

            .trip-progress {
                padding: 30px 20px;
                border-bottom: 1px solid #e0e0e0;
                background: linear-gradient(90deg, #fafafa 0%, #ffffff 100%);
            }

            .progress-header {
                margin-bottom: 25px;
            }

            .progress-header h2 {
                margin: 0 0 15px 0;
                font-size: 20px;
                font-weight: 600;
                color: #000000;
            }

            .progress-steps {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                position: relative;
                margin-top: 20px;
            }

            .progress-steps::before {
                content: '';
                position: absolute;
                top: 20px;
                left: 20px;
                right: 20px;
                height: 3px;
                background: linear-gradient(90deg, #e0e0e0 0%, #f0f0f0 100%);
                z-index: 1;
                border-radius: 2px;
            }

            .progress-step {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                flex: 1;
                z-index: 2;
                transition: all 0.3s ease;
            }

            .step-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #e0e0e0;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                margin-bottom: 10px;
                border: 3px solid #e0e0e0;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .step-text {
                font-size: 13px;
                color: #666;
                max-width: 90px;
                font-weight: 500;
                line-height: 1.2;
            }

            .step-time {
                font-size: 11px;
                color: #999;
                margin-top: 4px;
            }

            .progress-step.completed .step-icon {
                background: linear-gradient(135deg, #000000 0%, #333333 100%);
                color: #ffffff;
                border-color: #000000;
                transform: scale(1.1);
            }

            .progress-step.active .step-icon {
                background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
                color: #000000;
                border-color: #000000;
                box-shadow:
                    0 0 0 3px #ffffff,
                    0 0 0 6px #000000,
                    0 4px 12px rgba(0, 0, 0, 0.15);
                animation: pulse 2s infinite;
                transform: scale(1.15);
            }

            .progress-step.completed .step-text,
            .progress-step.active .step-text {
                color: #000000;
                font-weight: 600;
            }

            .trip-stats {
                padding: 25px 20px;
                border-bottom: 1px solid #e0e0e0;
                background: #fafafa;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
            }

            .stat-item {
                display: flex;
                align-items: center;
                background: #ffffff;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                transition: transform 0.2s ease;
            }

            .stat-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .stat-icon {
                font-size: 24px;
                margin-right: 12px;
            }

            .stat-details h4 {
                margin: 0 0 4px 0;
                font-size: 12px;
                color: #666;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .stat-details p {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #000000;
            }

            .route-info {
                padding: 30px 20px;
                border-bottom: 1px solid #e0e0e0;
                position: relative;
                background: #ffffff;
            }

            .location-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 30px;
                padding: 15px;
                background: #fafafa;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .location-item:hover {
                transform: translateX(5px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .location-item:last-child {
                margin-bottom: 0;
            }

            .location-icon {
                font-size: 24px;
                margin-right: 15px;
                margin-top: 5px;
            }

            .location-details h3 {
                margin: 0 0 8px 0;
                font-size: 18px;
                font-weight: 600;
                color: #000000;
            }

            .location-details p {
                margin: 0 0 5px 0;
                color: #333;
                font-size: 15px;
                line-height: 1.4;
            }

            .coordinates {
                font-family: monospace;
                font-size: 12px;
                color: #666;
                background: #f0f0f0;
                padding: 4px 8px;
                border-radius: 4px;
                display: inline-block;
            }

            .route-line {
                position: absolute;
                left: 50px;
                top: 90px;
                bottom: 90px;
                width: 4px;
                background: linear-gradient(180deg, #e0e0e0 0%, #000000 100%);
                border-radius: 2px;
                overflow: hidden;
            }

            .route-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                background: linear-gradient(180deg, #000000 0%, #333333 100%);
                border-radius: 2px;
                transition: height 0.8s ease;
            }

            .participants {
                padding: 30px 20px;
                border-bottom: 1px solid #e0e0e0;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                background: #fafafa;
            }

            .participant {
                background: #ffffff;
                border-radius: 12px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .participant::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #000000, #333333);
            }

            .participant:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }

            .client-card::before {
                background: linear-gradient(90deg, #4facfe, #00f2fe);
            }

            .driver-card::before {
                background: linear-gradient(90deg, #43e97b, #38f9d7);
            }

            .participant-avatar {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
                position: relative;
            }

            .avatar-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                margin-right: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .participant-details h3 {
                margin: 0 0 10px 0;
                font-size: 16px;
                font-weight: 600;
                color: #000000;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .participant-details .name {
                margin: 0 0 8px 0;
                color: #333;
                font-size: 18px;
                font-weight: 600;
            }

            .participant-details .phone {
                margin: 0 0 15px 0;
                color: #000000;
                font-size: 14px;
                font-weight: 600;
                font-family: monospace;
            }

            .car-details {
                margin: 10px 0;
                padding: 10px;
                background: #f8f8f8;
                border-radius: 6px;
            }

            .car-info {
                margin: 0 0 4px 0;
                font-weight: 600;
                color: #333;
            }

            .car-year {
                margin: 0 0 8px 0;
                color: #666;
                font-style: italic;
            }

            .license-plate {
                background: linear-gradient(135deg, #000000, #333333);
                color: #ffffff;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: 600;
                font-size: 14px;
                letter-spacing: 1px;
                display: inline-block;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .contact-actions {
                display: flex;
                gap: 8px;
                margin-top: 12px;
            }

            .contact-btn {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #f0f0f0;
                border: 2px solid #e0e0e0;
                color: #666;
                transition: all 0.2s ease;
            }

            .contact-btn:hover {
                background: #000000;
                color: #ffffff;
                border-color: #000000;
                transform: scale(1.1);
            }

            .status-message {
                padding: 30px 20px;
                border-bottom: 1px solid #000000;
            }

            .status-content {
                display: flex;
                align-items: flex-start;
                gap: 15px;
            }

            .status-text h3 {
                margin: 0 0 8px 0;
                font-size: 18px;
                font-weight: 600;
                color: #000000;
            }

            .status-text p {
                margin: 0 0 5px 0;
                color: #666;
                line-height: 1.5;
            }

            .location-info {
                font-family: monospace;
                font-size: 12px !important;
                background: #f5f5f5;
                padding: 8px;
                margin-top: 10px;
            }

            .status-message.waiting {
                background: #f9f9f9;
            }

            .status-message.in-progress {
                background: #f5f5f5;
            }

            .action-buttons {
                padding: 30px 20px;
                display: flex;
                gap: 15px;
                flex-wrap: wrap;
                background: #fafafa;
            }

            .action-btn {
                flex: 1;
                min-width: 180px;
                height: 55px;
                border: 2px solid #000000;
                border-radius: 8px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                font-size: 14px;
            }

            .action-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent
                );
                transition: left 0.5s ease;
            }

            .action-btn:hover::before {
                left: 100%;
            }

            .action-btn.primary {
                background: linear-gradient(135deg, #000000 0%, #333333 100%);
                color: #ffffff;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .action-btn.primary:hover:not(:disabled) {
                background: linear-gradient(135deg, #333333 0%, #555555 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
            }

            .action-btn.secondary {
                background: #ffffff;
                color: #000000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .action-btn.secondary:hover:not(:disabled) {
                background: #f5f5f5;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            }

            .action-btn.pulse {
                animation: pulse 2s infinite;
            }

            .action-btn:disabled {
                opacity: 0.7;
                cursor: not-allowed;
                transform: none !important;
            }

            .refresh-info {
                padding: 20px;
                background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #e0e0e0;
            }

            ::ng-deep .custom-progress-bar .p-progressbar-value {
                background: linear-gradient(90deg, #000000, #333333) !important;
            }

            ::ng-deep .user-chip {
                background: linear-gradient(
                    135deg,
                    #000000,
                    #333333
                ) !important;
                color: white !important;
                font-size: 11px !important;
                padding: 4px 8px !important;
            }

            @media (max-width: 768px) {
                .trip-info-container {
                    padding: 10px;
                    background: #ffffff;
                }

                .trip-card {
                    border-radius: 8px;
                }

                .participants {
                    grid-template-columns: 1fr;
                }

                .trip-stats {
                    grid-template-columns: 1fr;
                    gap: 10px;
                }

                .trip-title {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;
                }

                .action-buttons {
                    flex-direction: column;
                }

                .action-btn {
                    min-width: 100%;
                    height: 50px;
                }

                .refresh-info {
                    flex-direction: column;
                    gap: 8px;
                    text-align: center;
                }

                .progress-steps {
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 15px;
                }

                .progress-steps::before {
                    display: none;
                }

                .step-icon {
                    width: 35px;
                    height: 35px;
                    font-size: 14px;
                }

                .route-line {
                    display: none;
                }

                .location-item {
                    transform: none !important;
                }

                .participant {
                    transform: none !important;
                }
            }

            @media (max-width: 480px) {
                .trip-info-container {
                    padding: 5px;
                }

                .trip-header {
                    padding: 15px;
                }

                .trip-progress,
                .route-info,
                .participants,
                .action-buttons {
                    padding: 20px 15px;
                }

                .stat-item {
                    padding: 12px;
                }
            }

            /* Minimalist UI for DRIVER_DIDNT_ARRIVE status */
            .driver-didnt-arrive-container {
                padding: 1rem;
                max-width: 100%;
                margin: 0 auto;
            }

            .minimalist-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                padding: 1rem;
                background: linear-gradient(135deg, #000 0%, #333 100%);
                color: white;
                border-radius: 12px;
            }

            .minimalist-header h2 {
                color: white;
                margin: 0;
                font-size: 1.5rem;
                font-weight: 600;
            }

            .map-section {
                height: 400px;
                margin-bottom: 1rem;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .trip-info-minimal {
                background: white;
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            }

            .info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;
                border-bottom: 1px solid #f0f0f0;
            }

            .info-row:last-child {
                border-bottom: none;
            }

            .info-row .label {
                font-weight: 600;
                color: #666;
                font-size: 0.9rem;
            }

            .info-row .value {
                color: #000;
                font-weight: 500;
                text-align: right;
                flex: 1;
                margin-left: 1rem;
            }

            .action-buttons-minimal {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            /* Swipe Button Styles */
            .swipe-container {
                position: relative;
                width: 100%;
                height: 60px;
                background: linear-gradient(135deg, #000 0%, #333 100%);
                border: none;
                border-radius: 30px;
                color: white;
                font-size: 1.1rem;
                font-weight: 600;
                cursor: pointer;
                overflow: hidden;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                user-select: none;
                touch-action: none;
            }

            .swipe-container:hover:not(:disabled) {
                background: linear-gradient(135deg, #333 0%, #555 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            }

            .swipe-container:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            .swipe-track {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 30px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding-right: 20px;
                box-sizing: border-box;
            }

            .swipe-progress {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                background: linear-gradient(90deg, #e0e0e0 0%, #cccccc 100%);
                border-radius: 30px;
                transition: width 0.3s ease-in-out;
                z-index: 1;
            }

            .swipe-target {
                position: absolute;
                top: 0;
                right: 0;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 20px;
                box-sizing: border-box;
                z-index: 2;
            }

            .target-text {
                font-size: 1.1rem;
                font-weight: 600;
                color: #ffffff;
                margin-right: 8px;
            }

            .target-icon {
                font-size: 1.2rem;
            }

            .swipe-handle {
                position: absolute;
                top: 0;
                left: 0;
                width: 60px;
                height: 100%;
                border-radius: 30px;
                background: linear-gradient(135deg, #ffffff, #f0f0f0);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 3;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                transition: left 0.3s ease-in-out;
                cursor: grab;
            }

            .swipe-handle:active {
                cursor: grabbing;
            }

            .swipe-handle.swipe-active {
                background: linear-gradient(135deg, #ffffff, #e0e0e0);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transform: scale(1.05);
            }

            .swipe-container.swipe-completed .swipe-handle {
                left: calc(100% - 60px);
                background: linear-gradient(135deg, #333333, #555555);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .handle-icon {
                font-size: 1.2rem;
                color: #000000;
            }

            .swipe-loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(0, 0, 0, 0.5);
                border-radius: 30px;
                z-index: 4;
            }

            .update-location-btn {
                background: white;
                border: 2px solid #000;
                color: #000;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .update-location-btn:hover:not(:disabled) {
                background: #f8f9fa;
                transform: translateY(-2px);
            }

            .update-location-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .minimalist-header {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                }

                .map-section {
                    height: 300px;
                }

                .info-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }

                .info-row .value {
                    text-align: left;
                    margin-left: 0;
                }
            }
        