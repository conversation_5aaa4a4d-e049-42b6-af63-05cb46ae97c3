import { CommonModule } from '@angular/common';
import {
    Component,
    OnInit,
    OnDestroy,
    inject,
    signal,
    computed,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription, switchMap, startWith } from 'rxjs';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ProgressBarModule } from 'primeng/progressbar';
import { ChipModule } from 'primeng/chip';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService, ConfirmationService } from 'primeng/api';
import {
    OrderService,
    Trip,
    TripStatus,
    Order,
    OrderStatus,
} from '../../services/order.service';
import { GeolocationService } from '../../services/geolocation.service';
import { UserService } from '../../services/user.service';

// Import status-specific components
import { DriverDidntArriveComponent } from './components/driver-didnt-arrive/driver-didnt-arrive.component';
import { DriverWaitingClientComponent } from './components/driver-waiting-client/driver-waiting-client.component';
import { DriverWithClientComponent } from './components/driver-with-client/driver-with-client.component';
import { TripFinishedComponent } from './components/trip-finished/trip-finished.component';
import { TripStatusComponentData } from './components/base-trip-status.interface';
import { NominatimService } from '../../services/nominatim.service';
import { MapComponent } from '../../shared/components/map.component';
import { LatLng, latLng, Layer, marker, icon } from 'leaflet';

@Component({
    selector: 'app-trip-info',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ProgressSpinnerModule,
        ToastModule,
        DialogModule,
        ConfirmDialogModule,
        ProgressBarModule,
        ChipModule,
        TooltipModule,
        MapComponent,
        // Status-specific components
        DriverDidntArriveComponent,
        DriverWaitingClientComponent,
        DriverWithClientComponent,
        TripFinishedComponent,
    ],
    templateUrl: `./trip-info.component.html`,
    styleUrls: [`./trip-info.component.scss`],
})
export class TripInfoComponent implements OnInit, OnDestroy {
    // Services
    private route = inject(ActivatedRoute);
    private router = inject(Router);
    private orderService = inject(OrderService);
    private messageService = inject(MessageService);
    private confirmationService = inject(ConfirmationService);
    private geoService = inject(GeolocationService);
    private userService = inject(UserService);
    private nominatimService = inject(NominatimService);

    // Enums for template
    TripStatus = TripStatus;
    OrderStatus = OrderStatus;

    // Signals
    trip = signal<Trip | null>(null);
    order = signal<Order | null>(null);
    isLoading = signal<boolean>(false);
    isActionInProgress = signal<boolean>(false);
    isUpdatingLocation = signal<boolean>(false);
    lastUpdated = signal<Date>(new Date());
    currentUser = signal<any>(null);

    // Map-related signals
    mapNodes = signal<Layer[]>([]);
    mapCenter = signal<LatLng>(latLng(0, 0));
    mapZoom = signal<number>(16);

    // Location address cache
    pickupAddress = signal<string>('');
    dropoffAddress = signal<string>('');
    pickupAddressLoading = signal<boolean>(false);
    dropoffAddressLoading = signal<boolean>(false);

    // Subscriptions
    private refreshSubscription?: Subscription;
    private locationUpdateSubscription?: Subscription;

    // Computed properties
    isDriver = computed(() => {
        const user = this.currentUser();
        const trip = this.trip();
        return user && trip && user.id === trip.driverId;
    });

    // Component data for status-specific components
    componentData = computed((): TripStatusComponentData | null => {
        const trip = this.trip();
        const order = this.order();

        if (!trip || !order) return null;

        return {
            trip,
            order,
            isLoading: this.isLoading(),
            isActionInProgress: this.isActionInProgress(),
            isUpdatingLocation: this.isUpdatingLocation(),
            lastUpdated: this.lastUpdated(),
            pickupAddress: this.pickupAddress(),
            dropoffAddress: this.dropoffAddress(),
            // TODO: Calculate these from order data or external service
            estimatedDistance: undefined,
            estimatedDuration: undefined,
            estimatedPickupTime: undefined,
            estimatedDropoffTime: undefined,
            onUpdateLocation: () => this.updateLocation(),
            onGoBack: () => this.goBack(),
            onActionComplete: (action: string) =>
                this.handleActionComplete(action),
        };
    });

    progressSteps = computed(() => {
        const status = this.trip()?.status;
        const trip = this.trip();
        const createdAt = trip?.createdAt ? new Date(trip.createdAt) : null;

        const steps = [
            {
                key: 'en-route',
                icon: '🚗',
                text: 'En Route',
                className: '',
                timestamp: null as Date | null,
            },
            {
                key: 'waiting',
                icon: '⏰',
                text: 'Waiting',
                className: '',
                timestamp: null as Date | null,
            },
            {
                key: 'in-progress',
                icon: '🛣️',
                text: 'In Progress',
                className: '',
                timestamp: null as Date | null,
            },
            {
                key: 'completed',
                icon: '✅',
                text: 'Completed',
                className: '',
                timestamp: null as Date | null,
            },
        ];

        // Set timestamps based on trip status progression
        if (createdAt) {
            steps[0].timestamp = createdAt;
        }

        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                steps[0].className = 'active';
                break;
            case TripStatus.DRIVER_WAITING_CLIENT:
                steps[0].className = 'completed';
                steps[1].className = 'active';
                steps[1].timestamp = new Date(); // Approximate
                break;
            case TripStatus.DRIVER_WITH_CLIENT:
                steps[0].className = 'completed';
                steps[1].className = 'completed';
                steps[2].className = 'active';
                steps[1].timestamp = new Date(createdAt!.getTime() + 5 * 60000); // +5 min estimate
                steps[2].timestamp = new Date(
                    createdAt!.getTime() + 10 * 60000,
                ); // +10 min estimate
                break;
            case TripStatus.FINISHED:
                steps.forEach((step, index) => {
                    step.className = 'completed';
                    if (index === 0) step.timestamp = createdAt;
                    if (index === 1)
                        step.timestamp = new Date(
                            createdAt!.getTime() + 5 * 60000,
                        );
                    if (index === 2)
                        step.timestamp = new Date(
                            createdAt!.getTime() + 10 * 60000,
                        );
                    if (index === 3)
                        step.timestamp = new Date(
                            createdAt!.getTime() + 30 * 60000,
                        );
                });
                break;
        }

        return steps;
    });

    ngOnInit(): void {
        this.loadCurrentUser();
        this.loadTripData();
        this.startAutoRefresh();
        this.startAutoLocationUpdate();
    }

    ngOnDestroy(): void {
        if (this.refreshSubscription) {
            this.refreshSubscription.unsubscribe();
        }
        if (this.locationUpdateSubscription) {
            this.locationUpdateSubscription.unsubscribe();
        }
    }

    private loadCurrentUser(): void {
        this.userService.getProfile().subscribe((response: any) => {
            if (response.data) {
                this.currentUser.set(response.data);
            }
        });
    }

    private loadTripData(): void {
        const tripId = this.route.snapshot.paramMap.get('id');
        if (!tripId) {
            this.router.navigate(['/main']);
            return;
        }

        this.isLoading.set(true);
        this.orderService.getTripById(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                    // Load order data if available
                    if (response.data.order) {
                        console.log('Order data loaded:', response.data.order);
                        console.log(
                            'Pickup point:',
                            response.data.order.pickupPoint,
                        );
                        console.log(
                            'Dropoff point:',
                            response.data.order.dropoffPoint,
                        );
                        this.order.set(response.data.order);

                        // Load addresses for pickup and dropoff points
                        this.loadLocationAddresses();
                    }
                    // Setup map when trip data is loaded
                    this.setupMap();
                }
                this.isLoading.set(false);
                this.lastUpdated.set(new Date());
            },
            error: (error) => {
                console.error('Error loading trip:', error);
                this.isLoading.set(false);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load trip information',
                });
            },
        });
    }

    private startAutoRefresh(): void {
        this.refreshSubscription = interval(10000)
            .pipe(
                startWith(0),
                switchMap(() => {
                    const tripId = this.route.snapshot.paramMap.get('id');
                    if (tripId) {
                        return this.orderService.getTripById(tripId);
                    }
                    return [];
                }),
            )
            .subscribe({
                next: (response: any) => {
                    if (response.data) {
                        this.trip.set(response.data);
                        if (response.data.order) {
                            this.order.set(response.data.order);
                        }
                        this.lastUpdated.set(new Date());
                    }
                },
                error: (error) => {
                    console.error('Auto-refresh error:', error);
                },
            });
    }

    // Status helpers
    getStatusClass(): string {
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'en-route';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'waiting';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'in-progress';
            case TripStatus.FINISHED:
                return 'completed';
            default:
                return '';
        }
    }

    getStatusText(): string {
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'En Route';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'Waiting';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'In Progress';
            case TripStatus.FINISHED:
                return 'Completed';
            default:
                return 'Unknown';
        }
    }

    getStatusMessageClass(): string {
        const status = this.trip()?.status;
        if (status === TripStatus.DRIVER_WAITING_CLIENT) return 'waiting';
        if (status === TripStatus.DRIVER_WITH_CLIENT) return 'in-progress';
        return '';
    }

    getStatusTitle(): string {
        const isDriver = this.isDriver();
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return isDriver
                    ? 'Navigate to pickup location'
                    : 'Driver is on the way';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return isDriver ? 'Waiting for client' : 'Driver has arrived';
            case TripStatus.DRIVER_WITH_CLIENT:
                return isDriver ? 'Trip in progress' : 'Trip in progress';
            case TripStatus.FINISHED:
                return 'Trip completed successfully';
            default:
                return 'Trip status unknown';
        }
    }

    getStatusDescription(): string {
        const isDriver = this.isDriver();
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return isDriver
                    ? 'Navigate to the pickup location and mark as arrived when you reach the client.'
                    : 'Your driver is currently on the way to pick you up. You will be notified when they arrive.';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return isDriver
                    ? 'You have arrived at the pickup location. Please wait for the client and start the trip once they are in the vehicle.'
                    : 'Your driver has arrived at the pickup location. Please head to the vehicle to begin your trip.';
            case TripStatus.DRIVER_WITH_CLIENT:
                return isDriver
                    ? 'Trip is currently in progress. Complete the trip when you reach the destination.'
                    : 'Your trip is currently in progress. Sit back and enjoy the ride!';
            case TripStatus.FINISHED:
                return 'Thank you for using our service! This trip has been completed successfully.';
            default:
                return 'Loading trip status...';
        }
    }

    canPerformAction(): boolean {
        const trip = this.trip();
        return trip && trip.status !== TripStatus.FINISHED && this.isDriver();
    }

    // Driver actions
    markArrived(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.markDriverArrived(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking arrived:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark as arrived',
                });
                this.isActionInProgress.set(false);
            },
        });
    }

    markClientPacked(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.startTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking client packed:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark client as packed',
                });
                this.isActionInProgress.set(false);
            },
        });
    }

    startTrip(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.startTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error starting trip:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to start trip',
                });
                this.isActionInProgress.set(false);
            },
        });
    }

    completeTrip(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.completeTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error completing trip:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to complete trip',
                });
                this.isActionInProgress.set(false);
            },
        });
    }

    updateLocation(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isUpdatingLocation.set(true);
        this.geoService.getUserCountry().subscribe({
            next: (position: any) => {
                const locationDto = {
                    latitude: position.latitude,
                    longitude: position.longitude,
                };

                this.orderService
                    .updateTripLocation(tripId, locationDto)
                    .subscribe({
                        next: (response: any) => {
                            if (response.data) {
                                this.trip.set(response.data);
                            }
                            this.isUpdatingLocation.set(false);
                        },
                        error: (error: any) => {
                            console.error('Error updating location:', error);
                            this.messageService.add({
                                severity: 'error',
                                summary: 'Error',
                                detail: 'Failed to update location',
                            });
                            this.isUpdatingLocation.set(false);
                        },
                    });
            },
            error: (error: any) => {
                console.error('Error getting position:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to get current location',
                });
                this.isUpdatingLocation.set(false);
            },
        });
    }

    // Utility methods
    formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    }

    formatTime(date: Date): string {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        });
    }

    formatLocation(point: any): string {
        if (!point) return 'Location not available';
        return `${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)}`;
    }

    goBack(): void {
        this.router.navigate(['/main']);
    }

    handleActionComplete(action: string): void {
        switch (action) {
            case 'confirm_arrival':
                this.confirmArrival();
                break;
            case 'start_trip':
                this.confirmStartTrip();
                break;
            case 'complete_trip':
                this.confirmCompleteTrip();
                break;
            case 'rate_trip':
                // TODO: Implement rating functionality
                console.log('Rate trip action triggered');
                break;
            default:
                console.warn('Unknown action:', action);
        }
    }

    // New enhanced functionality methods

    /**
     * Get progress percentage for progress bar
     */
    getProgressPercentage(): number {
        const status = this.trip()?.status;
        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 25;
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 50;
            case TripStatus.DRIVER_WITH_CLIENT:
                return 75;
            case TripStatus.FINISHED:
                return 100;
            default:
                return 0;
        }
    }

    /**
     * Get trip duration string
     */
    getTripDuration(): string {
        const trip = this.trip();
        if (!trip?.createdAt) return 'N/A';

        const start = new Date(trip.createdAt);
        const now = new Date();
        const diffMs = now.getTime() - start.getTime();
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 60) {
            return `${diffMins} min`;
        } else {
            const hours = Math.floor(diffMins / 60);
            const minutes = diffMins % 60;
            return `${hours}h ${minutes}m`;
        }
    }

    /**
     * Get estimated distance (placeholder)
     */
    getEstimatedDistance(): string {
        // In a real app, this would calculate distance between pickup and dropoff
        const order = this.order();
        if (!order?.pickupPoint || !order?.dropoffPoint) return 'N/A';

        // Simple distance calculation (placeholder)
        const lat1 = order.pickupPoint.latitude;
        const lon1 = order.pickupPoint.longitude;
        const lat2 = order.dropoffPoint.latitude;
        const lon2 = order.dropoffPoint.longitude;

        const R = 6371; // Earth's radius in km
        const dLat = ((lat2 - lat1) * Math.PI) / 180;
        const dLon = ((lon2 - lon1) * Math.PI) / 180;
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((lat1 * Math.PI) / 180) *
                Math.cos((lat2 * Math.PI) / 180) *
                Math.sin(dLon / 2) *
                Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;

        return `${distance.toFixed(1)} km`;
    }

    /**
     * Get estimated fare (placeholder)
     */
    getEstimatedFare(): string {
        // In a real app, this would calculate based on distance, time, surge pricing, etc.
        const distance = parseFloat(this.getEstimatedDistance());
        if (isNaN(distance)) return 'N/A';

        const baseFare = 5;
        const perKmRate = 2.5;
        const estimatedFare = baseFare + distance * perKmRate;

        return `$${estimatedFare.toFixed(2)}`;
    }

    /**
     * Format location with address using reverse geocoding
     */
    formatLocationWithAddress(point: any): string {
        console.log('formatLocationWithAddress called with:', point);

        if (!point) {
            console.log('Point is null or undefined');
            return 'Location not available';
        }

        if (!point.latitude || !point.longitude) {
            console.log('Point missing latitude or longitude:', point);
            return 'Coordinates not available';
        }

        // Return cached address if available
        if (point.address) {
            return point.address;
        }

        // Return empty string to show skeleton loading
        return '';
    }

    /**
     * Load addresses for pickup and dropoff points using reverse geocoding
     */
    private loadLocationAddresses(): void {
        const order = this.order();
        if (!order) return;

        // Load pickup address
        if (
            order.pickupPoint &&
            order.pickupPoint.latitude &&
            order.pickupPoint.longitude
        ) {
            this.pickupAddressLoading.set(true);
            this.nominatimService
                .reverseGeocode(
                    order.pickupPoint.longitude,
                    order.pickupPoint.latitude,
                )
                .subscribe({
                    next: (result) => {
                        console.log('Pickup reverse geocoding result:', result);
                        if (result.display_name) {
                            (order.pickupPoint as any).address =
                                result.display_name;
                            this.pickupAddress.set(result.display_name);
                        }
                        this.pickupAddressLoading.set(false);
                    },
                    error: (error) => {
                        console.error('Pickup reverse geocoding error:', error);
                        this.pickupAddressLoading.set(false);
                    },
                });
        }

        // Load dropoff address
        if (
            order.dropoffPoint &&
            order.dropoffPoint.latitude &&
            order.dropoffPoint.longitude
        ) {
            this.dropoffAddressLoading.set(true);
            this.nominatimService
                .reverseGeocode(
                    order.dropoffPoint.longitude,
                    order.dropoffPoint.latitude,
                )
                .subscribe({
                    next: (result) => {
                        console.log(
                            'Dropoff reverse geocoding result:',
                            result,
                        );
                        if (result.display_name) {
                            (order.dropoffPoint as any).address =
                                result.display_name;
                            this.dropoffAddress.set(result.display_name);
                        }
                        this.dropoffAddressLoading.set(false);
                    },
                    error: (error) => {
                        console.error(
                            'Dropoff reverse geocoding error:',
                            error,
                        );
                        this.dropoffAddressLoading.set(false);
                    },
                });
        }
    }

    /**
     * Format phone number
     */
    formatPhoneNumber(phone: string | undefined): string {
        if (!phone) return 'N/A';

        // Basic phone number formatting
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        }
        return phone;
    }

    // Confirmation dialog methods
    confirmMarkArrived(): void {
        this.confirmationService.confirm({
            message: 'Are you sure you have arrived at the pickup location?',
            header: 'Confirm Arrival',
            icon: 'pi pi-map-marker',
            acceptLabel: 'Yes, I have arrived',
            rejectLabel: 'Cancel',
            accept: () => {
                this.markArrivedWithClientCheck();
            },
        });
    }

    markArrivedWithClientCheck(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.markDriverArrived(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                    // Now ask if client has arrived
                    this.checkClientArrival();
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking arrived:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark as arrived',
                });
                this.isActionInProgress.set(false);
            },
        });
    }

    checkClientArrival(): void {
        this.confirmationService.confirm({
            message: 'Has the client arrived at the pickup location?',
            header: 'Client Status',
            icon: 'pi pi-user',
            acceptLabel: 'Yes, client is here',
            rejectLabel: 'No, still waiting',
            accept: () => {
                // Client is here, start the trip immediately
                this.startTrip();
            },
            reject: () => {
                // Client is not here, stay in DRIVER_WAITING_CLIENT status
                this.messageService.add({
                    severity: 'info',
                    summary: 'Waiting for Client',
                    detail: 'You are now waiting for the client to arrive',
                });
            },
        });
    }

    confirmStartTrip(): void {
        this.confirmationService.confirm({
            message:
                'Is the client in the vehicle and ready to start the trip?',
            header: 'Start Trip',
            icon: 'pi pi-play',
            acceptLabel: 'Yes, start trip',
            rejectLabel: 'Cancel',
            accept: () => {
                this.startTrip();
            },
        });
    }

    confirmCompleteTrip(): void {
        this.confirmationService.confirm({
            message:
                'Have you reached the destination? This will complete the trip and process payment.',
            header: 'Complete Trip',
            icon: 'pi pi-check',
            acceptLabel: 'Yes, complete trip',
            rejectLabel: 'Cancel',
            accept: () => {
                this.completeTrip();
            },
        });
    }

    // Contact methods (placeholders)
    callClient(): void {
        const phone = this.order()?.user?.phoneNumber;
        if (phone) {
            // In a real app, this would initiate a call
            window.open(`tel:${phone}`);
        }
    }

    messageClient(): void {
        const phone = this.order()?.user?.phoneNumber;
        if (phone) {
            // In a real app, this would open SMS or in-app messaging
            window.open(`sms:${phone}`);
        }
    }

    callDriver(): void {
        const phone = this.trip()?.driver?.phoneNumber;
        if (phone) {
            window.open(`tel:${phone}`);
        }
    }

    messageDriver(): void {
        const phone = this.trip()?.driver?.phoneNumber;
        if (phone) {
            window.open(`sms:${phone}`);
        }
    }

    // Auto location update method
    private startAutoLocationUpdate(): void {
        if (this.isDriver()) {
            this.locationUpdateSubscription = interval(5000).subscribe(() => {
                this.updateLocation();
            });
        }
    }

    // Map methods
    private setupMap(): void {
        const trip = this.trip();
        const order = this.order();

        if (!trip || !order || !order.pickupPoint) return;

        const nodes: Layer[] = [];

        // Add pickup location marker with person icon
        const pickupMarker = marker(
            [order.pickupPoint.latitude, order.pickupPoint.longitude],
            {
                icon: icon({
                    iconUrl:
                        'https://cdn-icons-png.flaticon.com/512/1077/1077114.png', // Person icon
                    iconSize: [32, 32],
                    iconAnchor: [16, 32],
                    popupAnchor: [0, -32],
                    shadowSize: [0, 0],
                }),
            },
        );
        nodes.push(pickupMarker);

        // Add driver location marker with car icon if available
        if (trip.currentLocationLatitude && trip.currentLocationLongitude) {
            const driverMarker = marker(
                [trip.currentLocationLatitude, trip.currentLocationLongitude],
                {
                    icon: icon({
                        iconUrl:
                            'https://cdn-icons-png.flaticon.com/512/744/744465.png', // Car icon
                        iconSize: [32, 32],
                        iconAnchor: [16, 32],
                        popupAnchor: [0, -32],
                        shadowSize: [0, 0],
                    }),
                },
            );
            nodes.push(driverMarker);

            // Center map on driver location
            this.mapCenter.set(
                latLng(
                    trip.currentLocationLatitude,
                    trip.currentLocationLongitude,
                ),
            );
        } else {
            // Center map on pickup location if no driver location
            this.mapCenter.set(
                latLng(order.pickupPoint.latitude, order.pickupPoint.longitude),
            );
        }

        this.mapNodes.set(nodes);
    }

    // Swipe functionality
    isSliding = signal<boolean>(false);
    isSwipeCompleted = signal<boolean>(false);
    private startX = 0;
    private currentX = 0;
    private swipeThreshold = 50; // Minimum distance for a swipe

    startSlide(event: MouseEvent | TouchEvent): void {
        if (this.isActionInProgress()) return;

        this.isSliding.set(true);
        this.isSwipeCompleted.set(false);
        this.startX =
            event instanceof MouseEvent
                ? event.clientX
                : event.touches[0].clientX;
        this.currentX = this.startX;

        // Prevent default to avoid text selection
        event.preventDefault();
    }

    onSlide(event: MouseEvent | TouchEvent): void {
        if (!this.isSliding()) return;

        const currentX =
            event instanceof MouseEvent
                ? event.clientX
                : event.touches[0].clientX;
        const deltaX = currentX - this.startX;

        // Only allow rightward swiping
        if (deltaX > 0) {
            this.currentX = Math.min(
                currentX,
                this.startX + this.swipeThreshold,
            );
        }

        // Prevent default to avoid text selection
        event.preventDefault();
    }

    endSlide(event: MouseEvent | TouchEvent): void {
        if (!this.isSliding()) return;

        const currentX =
            event instanceof MouseEvent
                ? event.clientX
                : event.changedTouches[0].clientX;
        const deltaX = currentX - this.startX;

        if (deltaX >= this.swipeThreshold) {
            this.isSwipeCompleted.set(true);

            // Handle different actions based on trip status
            const trip = this.trip();
            if (trip?.status === TripStatus.DRIVER_DIDNT_ARRIVE) {
                this.markArrived();
            } else if (trip?.status === TripStatus.DRIVER_WAITING_CLIENT) {
                this.markClientPacked();
            }
        } else {
            this.isSwipeCompleted.set(false);
            this.currentX = this.startX; // Reset to start position
        }
        this.isSliding.set(false);
    }

    getSwipeProgress(): number {
        if (!this.isSliding() && !this.isSwipeCompleted()) {
            return 0;
        }

        if (this.isSwipeCompleted()) {
            return 100;
        }

        const progress = (this.currentX - this.startX) / this.swipeThreshold;
        return Math.min(Math.max(progress * 100, 0), 100);
    }

    getHandlePosition(): number {
        if (!this.isSliding() && !this.isSwipeCompleted()) {
            return 0;
        }

        if (this.isSwipeCompleted()) {
            return 85; // Leave some space for the handle
        }

        const progress = (this.currentX - this.startX) / this.swipeThreshold;
        const clampedProgress = Math.min(Math.max(progress, 0), 1);
        return clampedProgress * 85; // Max 85% to leave space for handle
    }
}
