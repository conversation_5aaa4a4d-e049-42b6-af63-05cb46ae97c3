import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    Order,
    OrderStatus,
    Trip,
    TripStatus,
    CreateOrderDto,
    DriverLocationDto,
    Point,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export { OrderStatus, TripStatus } from '../core/types/tripoos.types';
export type {
    Order,
    Trip,
    CreateOrderDto,
    DriverLocationDto,
    Point,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class OrderService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Calculate the initial price for an order before creating it
     * @param createOrderDto Order creation data
     * @returns Observable with calculated price
     */
    calculateOrderPrice(
        createOrderDto: CreateOrderDto,
    ): Observable<{ data: number; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: 'api/orders/calculate-initial-order-price',
            opj: createOrderDto,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for price calculation
            failedToast: true, // Show error toast if calculation fails
        };
        return this.http.post<number>(options);
    }

    /**
     * Create a new order
     * @param createOrderDto Order creation data
     * @returns Observable with Order data
     */
    createOrder(
        createOrderDto: CreateOrderDto,
    ): Observable<{ data: Order; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            successMessage: 'Your order was successful',
            failedMessage: 'Your order failed',
            link: 'api/orders',
            opj: createOrderDto,
            des: this.destroyRef,
            showloader: false, // Disable global loading spinner
            successToast: false, // Disable success toast since we handle it manually
            failedToast: false, // Disable automatic error toast so we can show specific backend message
        };
        return this.http.post<Order>(options);
    }

    /**
     * Find nearby pending orders for drivers
     * @param locationDto Driver's current location
     * @returns Observable with array of nearby Order objects
     */
    findNearbyPendingOrders(
        locationDto: DriverLocationDto,
    ): Observable<{ data: Order[]; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: 'api/orders/nearby',
            opj: locationDto,
            des: this.destroyRef,
        };
        return this.http.post<Order[]>(options);
    }

    /**
     * Accept an order as a driver
     * @param orderId ID of the order to accept
     * @returns Observable with Trip data
     */
    approveOrder(
        orderId: string,
    ): Observable<{ data: Order; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/${orderId}/approve`,
            des: this.destroyRef,
        };
        return this.http.post<Order>(options);
    }

    /**
     * Mark driver as arrived at pickup location
     * @param tripId ID of the trip
     * @returns Observable with updated Trip data
     */
    markDriverArrived(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/trips/${tripId}/arrived`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Start a trip
     * @param tripId ID of the trip to start
     * @returns Observable with updated Trip data
     */
    startTrip(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/trips/${tripId}/start`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Update trip location
     * @param tripId ID of the trip
     * @param locationDto Driver's current location
     * @returns Observable with updated Trip data
     */
    updateTripLocation(
        tripId: string,
        locationDto: DriverLocationDto,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/trips/${tripId}/location`,
            opj: locationDto,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Get trip by ID
     * @param tripId ID of the trip
     * @returns Observable with Trip data including driver and order information
     */
    getTripById(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/trips/${tripId}`,
            des: this.destroyRef,
        };
        return this.http.get<Trip>(options);
    }

    /**
     * Complete a trip
     * @param tripId ID of the trip to complete
     * @returns Observable with updated Trip data
     */
    completeTrip(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/trips/${tripId}/complete`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }
    cancleOrder(
        orderId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/orders/${orderId}/cancel`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    // These functions are not implemented in the backend API
    // Removed: cancelOrder, getActiveTrip, getPendingOrders

    /**
     * Get all orders for the current user
     * @returns Observable with array of Order objects including pickup, dropoff, and trip information
     */
    getUserOrders(): Observable<
        { data: Order[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/orders/me',
            des: this.destroyRef,
        };
        return this.http.get<Order[]>(options);
    }

    getUserOrdersNotComplete(): Observable<
        { data: Order[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/orders/me/notComplete',
            des: this.destroyRef,
        };
        return this.http.get<Order[]>(options);
    }
}
