import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common';
import { OrderService } from './order.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { DriverGuard } from '../common/guards/driver.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User, Order, Trip } from '@prisma/client';
import { CreateOrderDto, DriverLocationDto } from './dto';

@Controller('api/orders')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post('calculate-initial-order-price')
  @UseGuards(AuthGuard)
  getInitialOrderPrice(
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<number> {
    return this.orderService.getInitialOrderPrice(createOrderDto);
  }

  @Post()
  @UseGuards(AuthGuard)
  create(
    @Body() createOrderDto: CreateOrderDto,
    @GetUser() user: User,
  ): Promise<Order> {
    return this.orderService.create(user.id, createOrderDto);
  }

  @Post('nearby')
  @UseGuards(DriverGuard)
  findNearbyPendingOrders(
    @Body() locationDto: DriverLocationDto,
    @GetUser() driver: User,
  ): Promise<Order[]> {
    return this.orderService.findNearbyPendingOrders(
      driver.id,
      locationDto.latitude,
      locationDto.longitude,
    );
  }

  @Post(':orderId/approve')
  @UseGuards(DriverGuard)
  approveOrder(
    @Param('orderId') orderId: string,
    @GetUser() driver: User,
  ): Promise<Order> {
    return this.orderService.approveOrder(orderId, driver.id);
  }

  @Post('trips/:tripId/arrived')
  @UseGuards(DriverGuard)
  markDriverArrived(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.orderService.markDriverArrived(tripId, driver.id);
  }

  // driver calls this when client arrives
  @Post('trips/:tripId/start')
  @UseGuards(DriverGuard)
  startTrip(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.orderService.startTrip(tripId, driver.id);
  }

  @Post('trips/:tripId/location')
  @UseGuards(DriverGuard)
  updateTripLocation(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
    @Body() locationDto: DriverLocationDto,
  ): Promise<Trip> {
    return this.orderService.updateTripLocation(
      tripId,
      driver.id,
      locationDto.latitude,
      locationDto.longitude,
    );
  }

  // everyone with trip id must be able to see trip infromation (even non-authorized users)
  @Get('trips/:tripId')
  getTripById(@Param('tripId') tripId: string): Promise<Trip> {
    return this.orderService.getTripById(tripId);
  }

  @Post('trips/:tripId/complete')
  @UseGuards(DriverGuard)
  completeTrip(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.orderService.completeTrip(tripId, driver.id);
  }

  @Get('me')
  @UseGuards(AuthGuard)
  getUserOrders(@GetUser() user: User): Promise<Order[]> {
    return this.orderService.getUserOrders(user.id);
  }
  @Get('me/notComplete')
  @UseGuards(AuthGuard)
  getUserOrdersNotComplete(@GetUser() user: User): Promise<Order[]> {
    return this.orderService.getUserOrdersNotComplete(user.id);
  }
  @Post('trips/:tripId/cancle')
  @UseGuards(DriverGuard)
  cancleTrip(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.orderService.cancleTrip(tripId, driver.id);
  }
}
